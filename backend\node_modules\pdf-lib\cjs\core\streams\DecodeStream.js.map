{"version": 3, "file": "DecodeStream.js", "sourceRoot": "", "sources": ["../../../src/core/streams/DecodeStream.ts"], "names": [], "mappings": ";;;AAAA,oCAA4D;AAC5D,4DAA6D;AAE7D;;;;;;GAMG;AAEH,6EAA6E;AAC7E,6EAA6E;AAC7E,6EAA6E;AAC7E,UAAU;AACV,IAAM,WAAW,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;AAEtC;;GAEG;AACH;IAQE,sBAAY,oBAA6B;QACvC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QACb,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC;QAC1B,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC;QAC3B,IAAI,oBAAoB,EAAE;YACxB,yEAAyE;YACzE,OAAO,IAAI,CAAC,eAAe,GAAG,oBAAoB,EAAE;gBAClD,IAAI,CAAC,eAAe,IAAI,CAAC,CAAC;aAC3B;SACF;IACH,CAAC;IAED,sBAAI,iCAAO;aAAX;YACE,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,EAAE;gBAC3C,IAAI,CAAC,SAAS,EAAE,CAAC;aAClB;YACD,OAAO,IAAI,CAAC,YAAY,KAAK,CAAC,CAAC;QACjC,CAAC;;;OAAA;IAED,8BAAO,GAAP;QACE,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QACrB,OAAO,IAAI,CAAC,YAAY,IAAI,GAAG,EAAE;YAC/B,IAAI,IAAI,CAAC,GAAG,EAAE;gBACZ,OAAO,CAAC,CAAC,CAAC;aACX;YACD,IAAI,CAAC,SAAS,EAAE,CAAC;SAClB;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;IACjC,CAAC;IAED,gCAAS,GAAT;QACE,IAAM,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC1B,IAAM,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC1B,IAAI,EAAE,KAAK,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,EAAE;YAC1B,OAAO,CAAC,CAAC,CAAC;SACX;QACD,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;IACxB,CAAC;IAED,+BAAQ,GAAR;QACE,IAAM,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC1B,IAAM,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC1B,IAAM,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC1B,IAAM,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC1B,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;IAClD,CAAC;IAED,+BAAQ,GAAR,UAAS,MAAc,EAAE,YAAoB;QAApB,6BAAA,EAAA,oBAAoB;QAC3C,IAAI,GAAG,CAAC;QACR,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QAErB,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC;YAChC,GAAG,GAAG,GAAG,GAAG,MAAM,CAAC;YAEnB,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,YAAY,GAAG,GAAG,EAAE;gBAC3C,IAAI,CAAC,SAAS,EAAE,CAAC;aAClB;YACD,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;YACjC,IAAI,GAAG,GAAG,MAAM,EAAE;gBAChB,GAAG,GAAG,MAAM,CAAC;aACd;SACF;aAAM;YACL,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE;gBAChB,IAAI,CAAC,SAAS,EAAE,CAAC;aAClB;YACD,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC;SACzB;QAED,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QAChD,sEAAsE;QACtE,OAAO,YAAY,IAAI,CAAC,CAAC,QAAQ,YAAY,iBAAiB,CAAC;YAC7D,CAAC,CAAC,IAAI,iBAAiB,CAAC,QAAQ,CAAC;YACjC,CAAC,CAAC,QAAQ,CAAC;IACf,CAAC;IAED,+BAAQ,GAAR;QACE,IAAM,UAAU,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAClC,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,gCAAS,GAAT,UAAU,MAAc,EAAE,YAAoB;QAApB,6BAAA,EAAA,oBAAoB;QAC5C,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QAClD,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC;QACzB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,2BAAI,GAAJ,UAAK,CAAS;QACZ,IAAI,CAAC,CAAC,EAAE;YACN,CAAC,GAAG,CAAC,CAAC;SACP;QACD,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IAChB,CAAC;IAED,4BAAK,GAAL;QACE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;IACf,CAAC;IAED,oCAAa,GAAb,UAAc,KAAa,EAAE,MAAc,CAAC,UAAU;QACpD,IAAM,GAAG,GAAG,KAAK,GAAG,MAAM,CAAC;QAC3B,OAAO,IAAI,CAAC,YAAY,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;YAC5C,IAAI,CAAC,SAAS,EAAE,CAAC;SAClB;QACD,OAAO,IAAI,gBAAM,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;IAC3D,CAAC;IAED,6BAAM,GAAN;QACE,OAAO,CAAC,IAAI,CAAC,GAAG;YAAE,IAAI,CAAC,SAAS,EAAE,CAAC;QACnC,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;IACpD,CAAC;IAES,gCAAS,GAAnB;QACE,MAAM,IAAI,kCAAyB,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;IAC1E,CAAC;IAES,mCAAY,GAAtB,UAAuB,SAAiB;QACtC,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,SAAS,IAAI,MAAM,CAAC,UAAU,EAAE;YAClC,OAAO,MAAM,CAAC;SACf;QACD,IAAI,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC;QAChC,OAAO,IAAI,GAAG,SAAS,EAAE;YACvB,IAAI,IAAI,CAAC,CAAC;SACX;QACD,IAAM,OAAO,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;QACrC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACpB,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC;IACjC,CAAC;IAQH,mBAAC;AAAD,CAAC,AAnJD,IAmJC;AAED,kBAAe,YAAY,CAAC"}