{"version": 3, "file": "Stream.js", "sourceRoot": "", "sources": ["../../../src/core/streams/Stream.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;AAsBH;IAME,gBAAY,MAAkB,EAAE,KAAc,EAAE,MAAe;QAC7D,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC;QACpB,IAAI,CAAC,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC;QACxB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;QACtB,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IACtE,CAAC;IAED,sBAAI,0BAAM;aAAV;YACE,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;QAC/B,CAAC;;;OAAA;IAED,sBAAI,2BAAO;aAAX;YACE,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;QAC3B,CAAC;;;OAAA;IAED,wBAAO,GAAP;QACE,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,EAAE;YACxB,OAAO,CAAC,CAAC,CAAC;SACX;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;IAChC,CAAC;IAED,0BAAS,GAAT;QACE,IAAM,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC1B,IAAM,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC1B,IAAI,EAAE,KAAK,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,EAAE;YAC1B,OAAO,CAAC,CAAC,CAAC;SACX;QACD,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;IACxB,CAAC;IAED,yBAAQ,GAAR;QACE,IAAM,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC1B,IAAM,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC1B,IAAM,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC1B,IAAM,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC1B,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;IAClD,CAAC;IAED,4DAA4D;IAC5D,yBAAQ,GAAR,UAAS,MAAc,EAAE,YAAoB;QAApB,6BAAA,EAAA,oBAAoB;QAC3C,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QACrB,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC;QAExB,IAAI,CAAC,MAAM,EAAE;YACX,IAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YAC7C,8CAA8C;YAC9C,OAAO,YAAY,CAAC,CAAC,CAAC,IAAI,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;SAClE;aAAM;YACL,IAAI,GAAG,GAAG,GAAG,GAAG,MAAM,CAAC;YACvB,IAAI,GAAG,GAAG,MAAM,EAAE;gBAChB,GAAG,GAAG,MAAM,CAAC;aACd;YACD,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;YACf,IAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAC1C,8CAA8C;YAC9C,OAAO,YAAY,CAAC,CAAC,CAAC,IAAI,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;SAClE;IACH,CAAC;IAED,yBAAQ,GAAR;QACE,IAAM,UAAU,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAClC,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,0BAAS,GAAT,UAAU,MAAc,EAAE,YAAoB;QAApB,6BAAA,EAAA,oBAAoB;QAC5C,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QAClD,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC;QACzB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,qBAAI,GAAJ,UAAK,CAAS;QACZ,IAAI,CAAC,CAAC,EAAE;YACN,CAAC,GAAG,CAAC,CAAC;SACP;QACD,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IAChB,CAAC;IAED,sBAAK,GAAL;QACE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;IACxB,CAAC;IAED,0BAAS,GAAT;QACE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC;IACxB,CAAC;IAED,8BAAa,GAAb,UAAc,KAAa,EAAE,MAAc;QACzC,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;IAC/C,CAAC;IAED,uBAAM,GAAN;QACE,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IACH,aAAC;AAAD,CAAC,AArGD,IAqGC;AAED,kBAAe,MAAM,CAAC"}