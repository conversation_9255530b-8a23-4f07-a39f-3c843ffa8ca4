import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import axios from 'axios';
import './FormatSettings.css';
import Tooltip from '../components/common/Tooltip';

const FormatSettings = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const documentId = location.state?.documentId;
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [documentInfo, setDocumentInfo] = useState(null);
  
  // Format settings
  const [settings, setSettings] = useState({
    trimSize: '6x9',
    pageColor: 'white',
    marginTop: 1,
    marginBottom: 1,
    marginInside: 0.75,
    marginOutside: 0.75,
    hasBleed: false,
    paperType: 'white',
    pageNumbering: true,
    pageNumberPosition: 'bottom-center',
    fontName: 'Times New Roman',
    fontSize: 12,
    lineSpacing: 1.5,
    includeTOC: true,
    includeChapterBreaks: true
  });

  // Available options based on KDP guidelines based on KDP guidelines
  const trimSizeOptions = [
    { value: '5x8', label: '5" x 8" (12.7 x 20.32 cm)', description: 'Common for fiction, novellas, short non-fiction' },
    { value: '5.5x8.5', label: '5.5" x 8.5" (13.97 x 21.59 cm)', description: 'Popular size for fiction and non-fiction' },
    { value: '6x9', label: '6" x 9" (15.24 x 22.86 cm)', description: 'Standard for textbooks and larger novels (KDP recommended)' },
    { value: '7x10', label: '7" x 10" (17.78 x 25.4 cm)', description: 'Good for workbooks and textbooks' },
    { value: '8.5x11', label: '8.5" x 11" (21.59 x 27.94 cm)', description: 'Letter size, good for large-format books' }
    // KDP allows custom sizes within 4"–8.5" width and 6"–11.69" height
    // KDP allows custom sizes within 4"–8.5" width and 6"–11.69" height
  ];

  const paperTypeOptions = [
    { value: 'white', label: 'White Paper', description: 'Bright white paper, good contrast for text and images' },
    { value: 'cream', label: 'Cream Paper', description: 'Warmer tone, easier on the eyes, popular for fiction (only available for black & white interior) (only available for black & white interior)' }
  ];

  const fontOptions = [
    { value: 'Times New Roman', label: 'Times New Roman', description: 'Classic serif font, very readable' },
    { value: 'Garamond', label: 'Garamond', description: 'Elegant serif font with traditional look' },
    { value: 'Georgia', label: 'Georgia', description: 'Clear, readable serif designed for screens' },
    { value: 'Arial', label: 'Arial', description: 'Clean sans-serif font' },
    { value: 'Helvetica', label: 'Helvetica', description: 'Modern sans-serif with neutral appearance' },
    { value: 'Baskerville', label: 'Baskerville', description: 'Traditional serif with good readability' }
  ];
  
  // KDP gutter size requirements based on page count
  const gutterSizeByPageCount = [
    { maxPages: 150, minGutterSize: 0.375 },
    { maxPages: 300, minGutterSize: 0.5 },
    { maxPages: 500, minGutterSize: 0.625 },
    { maxPages: 700, minGutterSize: 0.75 },
    { maxPages: 828, minGutterSize: 0.875 }
  ];
  
  // Get minimum required gutter size based on page count
  const getMinGutterSize = (pageCount) => {
    for (const sizeRule of gutterSizeByPageCount) {
      if (pageCount <= sizeRule.maxPages) {
        return sizeRule.minGutterSize;
      }
    }
    return 0.875; // Default to largest size for very large books
  };
  


  // Fetch document info
  useEffect(() => {
    if (!documentId) {
      navigate('/upload');
      return;
    }

    const fetchDocumentInfo = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`/api/document/${documentId}`);
        setDocumentInfo(response.data);
        
        // Set initial settings based on document info if available
        if (response.data.recommendedSettings) {
          setSettings(prevSettings => ({
            ...prevSettings,
            ...response.data.recommendedSettings
          }));
        }
      } catch (err) {
        console.error('Error fetching document info:', err);
        setError('Failed to load document information. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchDocumentInfo();
  }, [documentId, navigate]);

  // Handle form field changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    // Update settings and handle dependent values
    setSettings(prevSettings => {
      const newSettings = {
        ...prevSettings,
        [name]: type === 'checkbox' ? checked : value
      };
      
      // Special handling for dependent settings
      if (name === 'hasBleed' && checked) {
        // When enabling bleed, ensure margins meet minimum requirements
        newSettings.marginTop = Math.max(newSettings.marginTop, 0.375);
        newSettings.marginBottom = Math.max(newSettings.marginBottom, 0.375);
        newSettings.marginOutside = Math.max(newSettings.marginOutside, 0.375);
        newSettings.marginInside = Math.max(newSettings.marginInside, getMinGutterSize(documentInfo?.pageCount || 150));
      }
      
      // When changing page color, handle paper type compatibility
      if (name === 'pageColor' && value === 'color') {
        // Color interior can only use white paper
        newSettings.paperType = 'white';
      }
      
      // When page count is very low, disable spine text
      if ((name === 'documentId' || name === 'pageCount') && documentInfo?.pageCount < 79) {
        newSettings.includeSpineText = false;
      }
      
      return newSettings;
    });
  };

  // Handle numeric input with limits, accounting for KDP requirements
  const handleNumericChange = (e, min, max) => {
    const { name, value } = e.target;
    const numValue = parseFloat(value);
    
    if (isNaN(numValue)) {
      setSettings(prevSettings => ({
        ...prevSettings,
        [name]: ''
      }));
      return;
    }
    
    // Get minimum value based on KDP requirements
    let actualMin = min;
    if (name === 'marginInside') {
      // Inside margin (gutter) has special minimum based on page count
      actualMin = getMinGutterSize(documentInfo?.pageCount || 150);
    } else if (settings.hasBleed && (name === 'marginTop' || name === 'marginBottom' || name === 'marginOutside')) {
      // For bleed documents, outside/top/bottom margins must be at least 0.375"
      actualMin = Math.max(min, 0.375);
    } else if (!settings.hasBleed && (name === 'marginTop' || name === 'marginBottom' || name === 'marginOutside')) {
      // For non-bleed documents, outside/top/bottom margins must be at least 0.25"
      actualMin = Math.max(min, 0.25);
    }
    
    if (numValue < actualMin) {
      setSettings(prevSettings => ({
        ...prevSettings,
        [name]: actualMin
      }));
    } else if (numValue > max) {
      setSettings(prevSettings => ({
        ...prevSettings,
        [name]: max
      }));
    } else {
      setSettings(prevSettings => ({
        ...prevSettings,
        [name]: numValue
      }));
    }
  };

  // Submit settings and navigate to preview
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      const response = await axios.post(`/api/document/${documentId}/settings`, settings);
      navigate('/preview', { 
        state: { 
          documentId,
          formatId: response.data.formatId 
        }
      });
    } catch (err) {
      console.error('Error saving settings:', err);
      setError(err.response?.data?.message || 'Failed to save formatting settings. Please try again.');
    }
  };

  if (loading) {
    return (
      <div className="format-settings-page">
        <div className="container">
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>Loading document information...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="format-settings-page">
        <div className="container">
          <div className="error-container">
            <h2>Error</h2>
            <p>{error}</p>
            <button 
              onClick={() => navigate('/upload')} 
              className="btn btn-primary"
            >
              Back to Upload
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="format-settings-page">
      <div className="container">
        <div className="format-settings-container">
          <h1>Format Your Book</h1>
          
          {documentInfo && (
            <div className="document-info">
              <h3>Document Information</h3>
              <div className="info-grid">
                <div className="info-item">
                  <span className="info-label">File Name:</span>
                  <span className="info-value">{documentInfo.fileName}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">Pages:</span>
                  <span className="info-value">{documentInfo.pageCount}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">Word Count:</span>
                  <span className="info-value">{documentInfo.wordCount.toLocaleString()}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">File Format:</span>
                  <span className="info-value">{documentInfo.fileFormat}</span>
                </div>
              </div>
            </div>
          )}
          
          <form onSubmit={handleSubmit}>
            <div className="settings-section">
              <h3>Book Size & Margins</h3>
              
              <div className="form-group">
                <label className="form-label" htmlFor="trimSize">
                  Trim Size 
                  <Tooltip content="The physical dimensions of your book. The 6×9 inch size is the most cost-effective option for most books." />
                </label>
                <select
                  id="trimSize"
                  name="trimSize"
                  value={settings.trimSize}
                  onChange={handleChange}
                  className="form-control"
                >
                  {trimSizeOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                <p className="form-hint">{trimSizeOptions.find(o => o.value === settings.trimSize)?.description}</p>
              </div>
              
              <div className="form-group">
                <label className="form-label">
                  Margins 
                  <Tooltip content="Margins are the white space around your text. Inside margins need to be larger to account for binding." />
                </label>
                <div className="margins-grid">
                  <div>
                    <label htmlFor="marginTop">Top (inches)</label>
                    <input
                      id="marginTop"
                      name="marginTop"
                      type="number"
                      step="0.1"
                      min="0.5"
                      max="3"
                      value={settings.marginTop}
                      onChange={(e) => handleNumericChange(e, 0.5, 3)}
                      className="form-control"
                    />
                  </div>
                  <div>
                    <label htmlFor="marginBottom">Bottom (inches)</label>
                    <input
                      id="marginBottom"
                      name="marginBottom"
                      type="number"
                      step="0.1"
                      min="0.5"
                      max="3"
                      value={settings.marginBottom}
                      onChange={(e) => handleNumericChange(e, 0.5, 3)}
                      className="form-control"
                    />
                  </div>
                  <div>
                    <label htmlFor="marginInside">Inside (inches)</label>
                    <input
                      id="marginInside"
                      name="marginInside"
                      type="number"
                      step="0.1"
                      min="0.5"
                      max="3"
                      value={settings.marginInside}
                      onChange={(e) => handleNumericChange(e, 0.5, 3)}
                      className="form-control"
                    />
                  </div>
                  <div>
                    <label htmlFor="marginOutside">Outside (inches)</label>
                    <input
                      id="marginOutside"
                      name="marginOutside"
                      type="number"
                      step="0.1"
                      min="0.5"
                      max="3"
                      value={settings.marginOutside}
                      onChange={(e) => handleNumericChange(e, 0.5, 3)}
                      className="form-control"
                    />
                  </div>
                </div>
              </div>
              
              <div className="form-group">
                <div className="checkbox-group">
                  <input
                    id="hasBleed"
                    name="hasBleed"
                    type="checkbox"
                    checked={settings.hasBleed}
                    onChange={handleChange}
                  />
                  <label htmlFor="hasBleed">
                    Include bleed 
                    <Tooltip content="Only enable bleed if your book contains images that extend to the edge of the page. This adds 0.125 inches to the outside edges." />
                  </label>
                </div>
                {settings.hasBleed && (
                  <div className="bleed-warning">
                    <p>
                      <strong>Important:</strong> KDP requires PDF format for books with bleed. 
                      {document.fileFormat !== 'PDF' && " Your document will be converted to PDF."}
                    </p>
                    <p className="small-text">
                      Bleed area (0.125" on top, bottom, and outside edge) will be trimmed during printing. 
                      Make sure all important content stays within the safe zone.
                    </p>
                  </div>
                )}
              </div>
            </div>
            
            <div className="settings-section">
              <h3>Paper & Appearance</h3>
              
              <div className="form-group">
                <label className="form-label" htmlFor="paperType">
                  Paper Type 
                  <Tooltip content="White paper provides better contrast for text and images. Cream paper is easier on the eyes for extended reading." />
                </label>
                <select
                  id="paperType"
                  name="paperType"
                  value={settings.paperType}
                  onChange={handleChange}
                  className="form-control"
                >
                  {paperTypeOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                <p className="form-hint">{paperTypeOptions.find(o => o.value === settings.paperType)?.description}</p>
                {settings.pageColor === 'color' && (
                  <div className="color-note">
                    <p className="small-text">
                      <strong>Note:</strong> Color interiors are only available with white paper.
                      {settings.paperType === 'cream' && ' Your paper type has been changed to white.'}
                    </p>
                  </div>
                )}
              </div>
              
              <div className="form-group">
                <label className="form-label" htmlFor="pageColor">
                  Interior Color 
                  <Tooltip content="Black & white is more affordable. Color is best for books with photos or colored illustrations." />
                </label>
                <select
                  id="pageColor"
                  name="pageColor"
                  value={settings.pageColor}
                  onChange={handleChange}
                  className="form-control"
                >
                  <option value="white">Black & White</option>
                  <option value="color">Full Color (Higher Cost)</option>
                </select>
                {settings.pageColor === 'color' && (
                  <div className="color-note">
                    <p className="small-text">
                      <strong>Note:</strong> Color interiors are only available with white paper.
                      {settings.paperType === 'cream' && ' Your paper type has been changed to white.'}
                    </p>
                  </div>
                )}
              </div>
            </div>
            
            <div className="settings-section">
              <h3>Text Formatting</h3>
              
              <div className="form-group">
                <label className="form-label" htmlFor="fontName">
                  Font 
                  <Tooltip content="Choose a font that's easy to read. Serif fonts (like Times New Roman) are traditional for books." />
                </label>
                <select
                  id="fontName"
                  name="fontName"
                  value={settings.fontName}
                  onChange={handleChange}
                  className="form-control"
                >
                  {fontOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              
              <div className="form-row">
                <div className="form-group">
                  <label className="form-label" htmlFor="fontSize">
                    Font Size 
                    <Tooltip content="11-12pt is standard for books. Larger sizes are easier to read but use more pages." />
                  </label>
                  <select
                    id="fontSize"
                    name="fontSize"
                    value={settings.fontSize}
                    onChange={handleChange}
                    className="form-control"
                  >
                    <option value="10">10pt (Small)</option>
                    <option value="11">11pt</option>
                    <option value="12">12pt (Standard)</option>
                    <option value="14">14pt (Larger)</option>
                    <option value="16">16pt (Very Large)</option>
                  </select>
                </div>
                
                <div className="form-group">
                  <label className="form-label" htmlFor="lineSpacing">
                    Line Spacing 
                    <Tooltip content="The space between lines of text. 1.5 is standard for easy reading." />
                  </label>
                  <select
                    id="lineSpacing"
                    name="lineSpacing"
                    value={settings.lineSpacing}
                    onChange={handleChange}
                    className="form-control"
                  >
                    <option value="1">Single (1.0)</option>
                    <option value="1.15">1.15</option>
                    <option value="1.5">1.5 (Standard)</option>
                    <option value="2">Double (2.0)</option>
                  </select>
                </div>
              </div>
            </div>
            
            <div className="settings-section">
              <h3>Book Structure</h3>
              
              <div className="form-group">
                <div className="checkbox-group">
                  <input
                    id="pageNumbering"
                    name="pageNumbering"
                    type="checkbox"
                    checked={settings.pageNumbering}
                    onChange={handleChange}
                  />
                  <label htmlFor="pageNumbering">
                    Add page numbers 
                    <Tooltip content="Page numbers are recommended for all books." />
                  </label>
                </div>
              </div>
              
              {settings.pageNumbering && (
                <div className="form-group">
                  <label className="form-label" htmlFor="pageNumberPosition">
                    Page Number Position
                  </label>
                  <select
                    id="pageNumberPosition"
                    name="pageNumberPosition"
                    value={settings.pageNumberPosition}
                    onChange={handleChange}
                    className="form-control"
                  >
                    <option value="bottom-center">Bottom Center</option>
                    <option value="bottom-outside">Bottom Outside</option>
                    <option value="top-center">Top Center</option>
                    <option value="top-outside">Top Outside</option>
                  </select>
                </div>
              )}
              
              <div className="form-group">
                <div className="checkbox-group">
                  <input
                    id="includeTOC"
                    name="includeTOC"
                    type="checkbox"
                    checked={settings.includeTOC}
                    onChange={handleChange}
                  />
                  <label htmlFor="includeTOC">
                    Generate Table of Contents 
                    <Tooltip content="Automatically create a table of contents based on your chapter headings." />
                  </label>
                </div>
              </div>
              
              <div className="form-group">
                <div className="checkbox-group">
                  <input
                    id="includeChapterBreaks"
                    name="includeChapterBreaks"
                    type="checkbox"
                    checked={settings.includeChapterBreaks}
                    onChange={handleChange}
                  />
                  <label htmlFor="includeChapterBreaks">
                    Force chapters to start on right pages 
                    <Tooltip content="Professional books typically start each chapter on a right-facing (odd-numbered) page." />
                  </label>
                </div>
              </div>
              

            </div>
            
            <div className="form-actions">
              <button type="button" className="btn btn-secondary" onClick={() => navigate('/upload')}>
                Back
              </button>
              <button type="submit" className="btn btn-primary">
                Preview Book
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default FormatSettings;