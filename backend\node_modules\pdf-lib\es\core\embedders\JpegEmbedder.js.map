{"version": 3, "file": "JpegEmbedder.js", "sourceRoot": "", "sources": ["../../../src/core/embedders/JpegEmbedder.ts"], "names": [], "mappings": ";AAGA,kBAAkB;AAClB,IAAM,OAAO,GAAG;IACd,MAAM,EAAE,MAAM,EAAE,MAAM;IACtB,MAAM,EAAE,MAAM,EAAE,MAAM;IACtB,MAAM,EAAE,MAAM,EAAE,MAAM;IACtB,MAAM,EAAE,MAAM,EAAE,MAAM;IACtB,MAAM,EAAE,MAAM,EAAE,MAAM;CACvB,CAAC;AAEF,IAAK,UAIJ;AAJD,WAAK,UAAU;IACb,uCAAyB,CAAA;IACzB,qCAAuB,CAAA;IACvB,uCAAyB,CAAA;AAC3B,CAAC,EAJI,UAAU,KAAV,UAAU,QAId;AAED,IAAM,mBAAmB,GAA8C;IACrE,CAAC,EAAE,UAAU,CAAC,UAAU;IACxB,CAAC,EAAE,UAAU,CAAC,SAAS;IACvB,CAAC,EAAE,UAAU,CAAC,UAAU;CACzB,CAAC;AAEF;;;;GAIG;AACH;IAkDE,sBACE,SAAqB,EACrB,gBAAwB,EACxB,KAAa,EACb,MAAc,EACd,UAAsB;QAEtB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IA7DY,gBAAG,GAAhB,UAAiB,SAAqB;;;;gBAC9B,QAAQ,GAAG,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;gBAE1C,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBAClC,IAAI,GAAG,KAAK,MAAM;oBAAE,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;gBAEzD,GAAG,GAAG,CAAC,CAAC;gBAGZ,OAAO,GAAG,GAAG,QAAQ,CAAC,UAAU,EAAE;oBAChC,MAAM,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;oBACjC,GAAG,IAAI,CAAC,CAAC;oBACT,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;wBAAE,MAAM;oBACpC,GAAG,IAAI,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;iBAChC;gBAED,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAO,CAAC;oBAAE,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;gBAChE,GAAG,IAAI,CAAC,CAAC;gBAEH,gBAAgB,GAAG,QAAQ,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC;gBAC5C,MAAM,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;gBACvC,GAAG,IAAI,CAAC,CAAC;gBAEH,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;gBACtC,GAAG,IAAI,CAAC,CAAC;gBAEH,WAAW,GAAG,QAAQ,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC;gBACvC,WAAW,GAAG,mBAAmB,CAAC,WAAW,CAAC,CAAC;gBAErD,IAAI,CAAC,WAAW;oBAAE,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;gBAErD,UAAU,GAAG,WAAW,CAAC;gBAE/B,sBAAO,IAAI,YAAY,CACrB,SAAS,EACT,gBAAgB,EAChB,KAAK,EACL,MAAM,EACN,UAAU,CACX,EAAC;;;KACH;IAuBK,uCAAgB,GAAtB,UAAuB,OAAmB,EAAE,GAAY;;;;gBAChD,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE;oBAC7C,IAAI,EAAE,SAAS;oBACf,OAAO,EAAE,OAAO;oBAChB,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;oBACvC,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,UAAU,EAAE,IAAI,CAAC,UAAU;oBAC3B,MAAM,EAAE,WAAW;oBAEnB,8DAA8D;oBAC9D,6DAA6D;oBAC7D,EAAE;oBACF,2DAA2D;oBAC3D,2DAA2D;oBAC3D,EAAE;oBACF,0DAA0D;oBAC1D,4CAA4C;oBAC5C,MAAM,EACJ,IAAI,CAAC,UAAU,KAAK,UAAU,CAAC,UAAU;wBACvC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBAC1B,CAAC,CAAC,SAAS;iBAChB,CAAC,CAAC;gBAEH,IAAI,GAAG,EAAE;oBACP,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;oBAC7B,sBAAO,GAAG,EAAC;iBACZ;qBAAM;oBACL,sBAAO,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAC;iBAClC;;;;KACF;IACH,mBAAC;AAAD,CAAC,AA/FD,IA+FC;AAED,eAAe,YAAY,CAAC"}