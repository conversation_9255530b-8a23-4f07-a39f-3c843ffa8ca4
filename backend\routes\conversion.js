const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs');

// Reference to documents store (in a real app, this would be a database)
const documents = {}; // This should be shared with document.js

// Generate final PDF
router.post('/:id/generate/:formatId', (req, res) => {
  const { id, formatId } = req.params;
  
  if (!documents[id]) {
    return res.status(404).json({
      error: true,
      message: 'Document not found'
    });
  }
  
  const document = documents[id];
  const settings = document.settings || {};
  
  // In a real application, this would involve complex PDF generation
  // For this demo, we'll simulate the process and return mock data
  
  // Generate a filename based on the original document
  const originalName = document.fileName;
  const baseName = path.basename(originalName, path.extname(originalName));
  const formattedName = `${baseName}-KDP-formatted.pdf`;
  
  // Mock data for the generated PDF
  const generatedPdf = {
    documentId: id,
    formatId,
    fileName: formattedName,
    fileSize: document.pageCount * 100000, // Mock file size based on page count
    pageCount: document.pageCount,
    downloadUrl: `/api/downloads/${id}/${formatId}/${formattedName}`,
    previewUrl: '/api/previews/pdf-cover.png', // Mock preview image
    createdAt: new Date().toISOString(),
    settings
  };
  
  // In a real app, we would store this information
  // For now, we'll just return it
  
  res.status(200).json(generatedPdf);
});

// Helper function to calculate final page count based on KDP guidelines
function calculateFinalPageCount(document, settings) {
  // This would be a complex calculation in a real application
  // Taking into account trim size, margins, font size, line spacing, etc.
  
  const { pageCount, wordCount } = document;
  const { trimSize, fontSize, lineSpacing, marginInside, marginOutside, marginTop, marginBottom, includeTOC } = settings;
  
  // Base words per page for 6x9 trim size with 12pt font and 1.5 line spacing
  const baseWordsPerPage = 250;
  
  // Adjust for trim size (available content area)
  let trimSizeFactor = 1.0; // 6x9 is baseline
  if (trimSize === '5x8') trimSizeFactor = 0.7;
  if (trimSize === '5.5x8.5') trimSizeFactor = 0.85;
  if (trimSize === '7x10') trimSizeFactor = 1.3;
  if (trimSize === '8.5x11') trimSizeFactor = 1.7;
  
  // Adjust for font size - smaller fonts allow more words per page
  const fontSizeFactor = 12 / fontSize;
  
  // Adjust for line spacing - tighter spacing allows more words per page
  const lineSpacingFactor = 1.5 / lineSpacing;
  
  // Adjust for margins - larger margins reduce content area
  // Calculate printable area reduction due to margins
  // For 6x9 baseline:
  const baseContentWidth = 6 - 0.75 - 0.75; // 6" width - 0.75" margins each side = 4.5" content width
  const baseContentHeight = 9 - 0.75 - 0.75; // 9" height - 0.75" margins top/bottom = 7.5" content height
  
  // Get dimensions from trim size
  const [widthStr, heightStr] = trimSize.split('x');
  const width = parseFloat(widthStr);
  const height = parseFloat(heightStr);
  
  // Calculate actual content area with user's margins
  const actualContentWidth = width - marginInside - marginOutside;
  const actualContentHeight = height - marginTop - marginBottom;
  
  // Calculate content area factor
  const contentAreaFactor = (actualContentWidth * actualContentHeight) / (baseContentWidth * baseContentHeight);
  
  // Calculate adjusted words per page
  const adjustedWordsPerPage = baseWordsPerPage * trimSizeFactor * fontSizeFactor * lineSpacingFactor * contentAreaFactor;
  
  // Calculate final page count
  let finalPageCount = Math.ceil(wordCount / adjustedWordsPerPage);
  
  // Make sure page count is even (KDP requires even page count)
  if (finalPageCount % 2 !== 0) {
    finalPageCount++;
  }
  
  // Add pages for front matter
  finalPageCount += 6; // Title page, copyright page, dedication, etc.
  
  // Add pages for TOC if included
  if (includeTOC) {
    // Estimate 30 chapters/sections per TOC page
    const tocPages = Math.ceil(pageCount / 30);
    finalPageCount += tocPages;
  }
  
  // Ensure minimum 24 pages as required by KDP
  finalPageCount = Math.max(finalPageCount, 24);
  
  return finalPageCount;
}

// Calculate spine width based on KDP's formulas
function calculateSpineWidth(pageCount, paperType, colorType) {
  // KDP spine width formulas:
  // - Black & white on white paper: 0.002252" per page
  // - Black & white on cream paper: 0.0025" per page
  // - Standard color on white paper: 0.002252" per page
  // - Premium color on white paper: 0.002347" per page
  
  if (paperType === 'cream' && colorType === 'white') {
    // Black & white on cream
    return pageCount * 0.0025;
  } else if (paperType === 'white' && colorType === 'color') {
    // Standard color on white (using standard color formula)
    return pageCount * 0.002252;
  } else {
    // Black & white on white (default)
    return pageCount * 0.002252;
  }
}

module.exports = router;