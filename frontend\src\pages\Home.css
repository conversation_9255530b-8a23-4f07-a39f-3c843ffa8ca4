/* General page styling */

/* Hero section */
.hero {
  background-color: #f8f9fa;
  padding: 5rem 0;
  margin-bottom: 3rem;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.hero h1 {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  color: #212529;
}

.lead {
  font-size: 1.25rem;
  line-height: 1.6;
  color: #6c757d;
  margin-bottom: 2rem;
}

.hero-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

/* Features section */
.features {
  padding: 3rem 0;
}

.section-title {
  margin-bottom: 3rem;
  font-size: 2rem;
  color: #212529;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.feature-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  text-align: center;
  transition: transform 0.3s, box-shadow 0.3s;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  margin: 0 auto 1.5rem;
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
  background-color: #0d6efd;
  border-radius: 50%;
}

.feature-card h3 {
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.feature-card p {
  color: #6c757d;
  line-height: 1.6;
}

/* Why use section */
.why-use {
  background-color: #f8f9fa;
  padding: 4rem 0;
  margin: 3rem 0;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.benefit-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.benefit-card h3 {
  margin-bottom: 1rem;
  font-size: 1.25rem;
  color: #0d6efd;
}

.benefit-card p {
  color: #6c757d;
  line-height: 1.6;
}

/* CTA section */
.cta {
  padding: 3rem 0;
}

.cta-content {
  max-width: 700px;
  margin: 0 auto;
  text-align: center;
}

.cta h2 {
  margin-bottom: 1rem;
  font-size: 2rem;
}

.cta p {
  margin-bottom: 2rem;
  color: #6c757d;
  font-size: 1.1rem;
}

/* Responsive styles */
@media (max-width: 768px) {
  .hero {
    padding: 3rem 0;
  }
  
  .hero h1 {
    font-size: 2rem;
  }
  
  .lead {
    font-size: 1.1rem;
  }
  
  .hero-buttons {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .section-title {
    font-size: 1.75rem;
    margin-bottom: 2rem;
  }
}