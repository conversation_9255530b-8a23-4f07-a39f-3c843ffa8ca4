{"version": 3, "file": "ViewerPreferences.d.ts", "sourceRoot": "", "sources": ["../../../src/core/interactive/ViewerPreferences.ts"], "names": [], "mappings": "AAAA,OAAO,QAAQ,4BAAkC;AACjD,OAAO,OAAO,2BAAiC;AAC/C,OAAO,OAAO,2BAAiC;AAC/C,OAAO,OAAO,2BAAiC;AAC/C,OAAO,SAAS,6BAAmC;AACnD,OAAO,UAAU,sBAA4B;AAgB7C,oBAAY,qBAAqB;IAC/B;;;OAGG;IACH,OAAO,YAAY;IAEnB,6EAA6E;IAC7E,WAAW,gBAAgB;IAE3B,yEAAyE;IACzE,SAAS,cAAc;IAEvB;;;OAGG;IACH,KAAK,UAAU;CAChB;AAED,oBAAY,gBAAgB;IAC1B,sDAAsD;IACtD,GAAG,QAAQ;IAEX;;;OAGG;IACH,GAAG,QAAQ;CACZ;AAED,oBAAY,YAAY;IACtB,uBAAuB;IACvB,IAAI,SAAS;IAGb,UAAU,eAAe;CAC1B;AAED,oBAAY,MAAM;IAChB,gDAAgD;IAChD,OAAO,YAAY;IAEnB;;;OAGG;IACH,mBAAmB,wBAAwB;IAE3C;;;OAGG;IACH,kBAAkB,uBAAuB;CAC1C;AAED,aAAK,iBAAiB,GAClB,aAAa,GACb,aAAa,GACb,cAAc,GACd,WAAW,GACX,cAAc,GACd,iBAAiB,GACjB,mBAAmB,CAAC;AACxB,aAAK,iBAAiB,GAClB,uBAAuB,GACvB,WAAW,GACX,cAAc,GACd,QAAQ,CAAC;AAEb,UAAU,SAAS;IACjB,KAAK,EAAE,MAAM,CAAC;IACd,GAAG,EAAE,MAAM,CAAC;CACb;AAED,cAAM,iBAAiB;IACrB,cAAc;IACd,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC;IAEvB,cAAc;IACd,MAAM,CAAC,QAAQ,SAAU,OAAO,KAAG,iBAAiB,CACtB;IAE9B,cAAc;IACd,MAAM,CAAC,MAAM,YAAa,UAAU,uBAGlC;IAEF,cAAc;IACd,SAAS,aAAa,IAAI,EAAE,OAAO;IAInC,SAAS,CAAC,UAAU,CAAC,GAAG,EAAE,iBAAiB,GAAG,OAAO,GAAG,SAAS;IAMjE,SAAS,CAAC,UAAU,CAAC,GAAG,EAAE,iBAAiB,GAAG,OAAO,GAAG,SAAS;IAMjE,cAAc;IACd,WAAW,IAAI,OAAO,GAAG,SAAS;IAIlC,cAAc;IACd,WAAW,IAAI,OAAO,GAAG,SAAS;IAIlC,cAAc;IACd,YAAY,IAAI,OAAO,GAAG,SAAS;IAInC,cAAc;IACd,SAAS,IAAI,OAAO,GAAG,SAAS;IAIhC,cAAc;IACd,YAAY,IAAI,OAAO,GAAG,SAAS;IAInC,cAAc;IACd,eAAe,IAAI,OAAO,GAAG,SAAS;IAItC,cAAc;IACd,qBAAqB,IAAI,OAAO,GAAG,SAAS;IAI5C,cAAc;IACd,SAAS,IAAI,OAAO,GAAG,SAAS;IAIhC,cAAc;IACd,YAAY,IAAI,OAAO,GAAG,SAAS;IAInC,cAAc;IACd,MAAM,IAAI,OAAO,GAAG,SAAS;IAI7B,cAAc;IACd,iBAAiB,IAAI,OAAO,GAAG,SAAS;IAIxC,cAAc;IACd,cAAc,IAAI,QAAQ,GAAG,SAAS;IAMtC,cAAc;IACd,SAAS,IAAI,SAAS,GAAG,SAAS;IAMlC;;;;OAIG;IACH,cAAc,IAAI,OAAO;IAIzB;;;;OAIG;IACH,cAAc,IAAI,OAAO;IAIzB;;;;;OAKG;IACH,eAAe,IAAI,OAAO;IAI1B;;;;OAIG;IACH,YAAY,IAAI,OAAO;IAIvB;;;;OAIG;IACH,eAAe,IAAI,OAAO;IAI1B;;;;;;OAMG;IACH,kBAAkB,IAAI,OAAO;IAI7B;;;;OAIG;IACH,wBAAwB,IAAI,qBAAqB;IAKjD;;;OAGG;IACH,mBAAmB,IAAI,gBAAgB;IAKvC;;;;OAIG;IACH,eAAe,IAAI,YAAY;IAK/B;;;;OAIG;IACH,SAAS,IAAI,MAAM,GAAG,SAAS;IAK/B;;;;;OAKG;IACH,oBAAoB,IAAI,OAAO,GAAG,SAAS;IAI3C;;;;;;;;;;;;;;;;OAgBG;IACH,iBAAiB,IAAI,SAAS,EAAE;IAkBhC;;;;OAIG;IACH,YAAY,IAAI,MAAM;IAItB;;;;OAIG;IACH,cAAc,CAAC,WAAW,EAAE,OAAO;IAKnC;;;;OAIG;IACH,cAAc,CAAC,WAAW,EAAE,OAAO;IAKnC;;;;;OAKG;IACH,eAAe,CAAC,YAAY,EAAE,OAAO;IAKrC;;;;OAIG;IACH,YAAY,CAAC,SAAS,EAAE,OAAO;IAK/B;;;;OAIG;IACH,eAAe,CAAC,YAAY,EAAE,OAAO;IAKrC;;;;;OAKG;IACH,kBAAkB,CAAC,YAAY,EAAE,OAAO;IAKxC;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,wBAAwB,CAAC,qBAAqB,EAAE,qBAAqB;IAUrE;;;;;;;;;;;;;;;;;OAiBG;IACH,mBAAmB,CAAC,gBAAgB,EAAE,gBAAgB;IAMtD;;;;;;;;;;;;;;OAcG;IACH,eAAe,CAAC,YAAY,EAAE,YAAY;IAM1C;;;;;;;;;;;;;;OAcG;IACH,SAAS,CAAC,MAAM,EAAE,MAAM;IAMxB;;;;;;;;;;;;OAYG;IACH,oBAAoB,CAAC,iBAAiB,EAAE,OAAO;IAK/C;;;;;;;;;;;;;;;;;;;;;;;;;OAyBG;IACH,iBAAiB,CAAC,cAAc,EAAE,SAAS,EAAE,GAAG,SAAS;IAezD;;;;OAIG;IACH,YAAY,CAAC,SAAS,EAAE,MAAM;CAM/B;AAED,eAAe,iBAAiB,CAAC"}