{"version": 3, "file": "CMap.js", "sourceRoot": "", "sources": ["../../../src/core/embedders/CMap.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,sBAAsB,EAAE,oBAAkB;AAChE,OAAO,EACL,aAAa,EACb,aAAa,EACb,WAAW,EACX,YAAY,GACb,4BAA0B;AAK3B,mDAAmD;AACnD,MAAM,CAAC,IAAM,UAAU,GAAG,UAAC,MAAe,EAAE,OAA8B;IACxE,IAAM,OAAO,GAAa,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACnD,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;QACvD,IAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;QAC1B,IAAM,EAAE,GAAG,aAAa,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACxD,IAAM,OAAO,GAAG,aAAa,eAAI,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC,CAAC;QAC5E,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;KAC9B;IAED,OAAO,gBAAgB,CAAC,OAAO,CAAC,CAAC;AACnC,CAAC,CAAC;AAEF,gFAAgF;AAEhF,IAAM,gBAAgB,GAAG,UAAC,OAAiB,IAAK,OAAA,qQAc9C,OAAO,CAAC,MAAM,sBACd,OAAO,CAAC,GAAG,CAAC,UAAC,EAAoB;QAAnB,OAAO,QAAA,EAAE,SAAS,QAAA;IAAM,OAAG,OAAO,SAAI,SAAW;AAAzB,CAAyB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,kFAM5E,EArB+C,CAqB/C,CAAC;AAEF,gFAAgF;AAEhF,IAAM,aAAa,GAAG;IAAC,gBAAmB;SAAnB,UAAmB,EAAnB,qBAAmB,EAAnB,IAAmB;QAAnB,2BAAmB;;IAAK,OAAA,MAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,MAAG;AAAtB,CAAsB,CAAC;AAEtE,IAAM,aAAa,GAAG,UAAC,KAAa,IAAK,OAAA,sBAAsB,CAAC,KAAK,EAAE,CAAC,CAAC,EAAhC,CAAgC,CAAC;AAE1E,IAAM,mBAAmB,GAAG,UAAC,SAAiB;IAC5C,IAAI,WAAW,CAAC,SAAS,CAAC;QAAE,OAAO,aAAa,CAAC,SAAS,CAAC,CAAC;IAE5D,IAAI,aAAa,CAAC,SAAS,CAAC,EAAE;QAC5B,IAAM,EAAE,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC;QACpC,IAAM,EAAE,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC;QACnC,OAAO,KAAG,aAAa,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,EAAE,CAAG,CAAC;KACnD;IAED,IAAM,GAAG,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC;IACnC,IAAM,GAAG,GAAG,OAAK,GAAG,+CAA4C,CAAC;IACjE,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;AACvB,CAAC,CAAC"}