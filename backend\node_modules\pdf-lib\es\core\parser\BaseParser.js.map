{"version": 3, "file": "BaseParser.js", "sourceRoot": "", "sources": ["../../../src/core/parser/BaseParser.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,kBAAkB,EAAE,kBAAwB;AAErD,OAAO,SAAS,4BAAkC;AAClD,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,0BAAgC;AAC7D,OAAO,EAAE,YAAY,EAAE,6BAAmC;AAC1D,OAAO,EAAE,YAAY,EAAE,oBAAkB;AAEjC,IAAA,OAAO,GAAqB,SAAS,QAA9B,EAAE,cAAc,GAAK,SAAS,eAAd,CAAe;AAE9C,uEAAuE;AACvE;IAIE,oBAAY,KAAiB,EAAE,UAAkB;QAAlB,2BAAA,EAAA,kBAAkB;QAC/C,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IAES,gCAAW,GAArB;QACE,IAAI,KAAK,GAAG,EAAE,CAAC;QAEf,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE;YACzB,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAC/B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;gBAAE,MAAM;YAC1B,KAAK,IAAI,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;SAC1C;QAED,IAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QAElC,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;YACpC,MAAM,IAAI,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;SAC5D;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,yCAAyC;IACzC,8EAA8E;IACpE,mCAAc,GAAxB;QACE,IAAI,KAAK,GAAG,EAAE,CAAC;QAEf,oDAAoD;QACpD,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE;YACzB,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAC/B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAAE,MAAM;YAC5B,KAAK,IAAI,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;YACzC,IAAI,IAAI,KAAK,SAAS,CAAC,MAAM;gBAAE,MAAM;SACtC;QAED,yCAAyC;QACzC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE;YACzB,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAC/B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;gBAAE,MAAM;YAC1B,KAAK,IAAI,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;SAC1C;QAED,IAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QAElC,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;YACpC,MAAM,IAAI,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;SAC5D;QAED,IAAI,WAAW,GAAG,MAAM,CAAC,gBAAgB,EAAE;YACzC,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,IAAM,GAAG,GAAG,2DAAyD,KAAK,6CAA0C,CAAC;gBACrH,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAClB,OAAO,MAAM,CAAC,gBAAgB,CAAC;aAChC;iBAAM;gBACL,IAAM,GAAG,GAAG,2DAAyD,KAAK,mBAAgB,CAAC;gBAC3F,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACnB;SACF;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAES,mCAAc,GAAxB;QACE,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE;YAC5D,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;SACnB;IACH,CAAC;IAES,6BAAQ,GAAlB;QACE,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE;YACzB,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAC/B,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,cAAc;gBAAE,OAAO;YACxD,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;SACnB;IACH,CAAC;IAES,gCAAW,GAArB;QACE,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,SAAS,CAAC,OAAO;YAAE,OAAO,KAAK,CAAC;QAC1D,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE;YACzB,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAC/B,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,cAAc;gBAAE,OAAO,IAAI,CAAC;YAC7D,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;SACnB;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAES,8CAAyB,GAAnC;QACE,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,OAAO,IAAI,CAAC,WAAW,EAAE;YAAE,IAAI,CAAC,cAAc,EAAE,CAAC;IACnD,CAAC;IAES,iCAAY,GAAtB,UAAuB,OAAiB;QACtC,IAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;QAC1C,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YACxD,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,OAAO,CAAC,GAAG,CAAC,EAAE;gBAC3D,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;gBACjC,OAAO,KAAK,CAAC;aACd;SACF;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IACH,iBAAC;AAAD,CAAC,AA1GD,IA0GC;AAED,eAAe,UAAU,CAAC"}