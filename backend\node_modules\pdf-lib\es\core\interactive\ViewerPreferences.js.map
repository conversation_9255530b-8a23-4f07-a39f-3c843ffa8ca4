{"version": 3, "file": "ViewerPreferences.js", "sourceRoot": "", "sources": ["../../../src/core/interactive/ViewerPreferences.ts"], "names": [], "mappings": "AAAA,OAAO,QAAQ,4BAAkC;AACjD,OAAO,OAAO,2BAAiC;AAE/C,OAAO,OAAO,2BAAiC;AAC/C,OAAO,SAAS,6BAAmC;AAEnD,OAAO,EACL,YAAY,EACZ,aAAa,EACb,aAAa,EACb,WAAW,GACZ,oBAAkB;AAEnB,IAAM,MAAM,GAAG,UACb,QAAuB,EACvB,QAAW;IAEX,IAAI,QAAQ,KAAK,SAAS;QAAE,OAAO,SAAS,CAAC;IAC7C,OAAO,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC5B,CAAC,CAAC;AAEF,MAAM,CAAN,IAAY,qBAkBX;AAlBD,WAAY,qBAAqB;IAC/B;;;OAGG;IACH,4CAAmB,CAAA;IAEnB,6EAA6E;IAC7E,oDAA2B,CAAA;IAE3B,yEAAyE;IACzE,gDAAuB,CAAA;IAEvB;;;OAGG;IACH,wCAAe,CAAA;AACjB,CAAC,EAlBW,qBAAqB,KAArB,qBAAqB,QAkBhC;AAED,MAAM,CAAN,IAAY,gBASX;AATD,WAAY,gBAAgB;IAC1B,sDAAsD;IACtD,+BAAW,CAAA;IAEX;;;OAGG;IACH,+BAAW,CAAA;AACb,CAAC,EATW,gBAAgB,KAAhB,gBAAgB,QAS3B;AAED,MAAM,CAAN,IAAY,YAMX;AAND,WAAY,YAAY;IACtB,uBAAuB;IACvB,6BAAa,CAAA;IAEb,iDAAiD;IACjD,yCAAyB,CAAA;AAC3B,CAAC,EANW,YAAY,KAAZ,YAAY,QAMvB;AAED,MAAM,CAAN,IAAY,MAeX;AAfD,WAAY,MAAM;IAChB,gDAAgD;IAChD,6BAAmB,CAAA;IAEnB;;;OAGG;IACH,qDAA2C,CAAA;IAE3C;;;OAGG;IACH,mDAAyC,CAAA;AAC3C,CAAC,EAfW,MAAM,KAAN,MAAM,QAejB;AAqBD;IAcE,cAAc;IACd,2BAAsB,IAAa;QACjC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAES,sCAAU,GAApB,UAAqB,GAAsB;QACzC,IAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QACpD,IAAI,SAAS,YAAY,OAAO;YAAE,OAAO,SAAS,CAAC;QACnD,OAAO,SAAS,CAAC;IACnB,CAAC;IAES,sCAAU,GAApB,UAAqB,GAAsB;QACzC,IAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QACpD,IAAI,SAAS,YAAY,OAAO;YAAE,OAAO,SAAS,CAAC;QACnD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,cAAc;IACd,uCAAW,GAAX;QACE,OAAO,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;IACxC,CAAC;IAED,cAAc;IACd,uCAAW,GAAX;QACE,OAAO,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;IACxC,CAAC;IAED,cAAc;IACd,wCAAY,GAAZ;QACE,OAAO,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;IACzC,CAAC;IAED,cAAc;IACd,qCAAS,GAAT;QACE,OAAO,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;IACtC,CAAC;IAED,cAAc;IACd,wCAAY,GAAZ;QACE,OAAO,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;IACzC,CAAC;IAED,cAAc;IACd,2CAAe,GAAf;QACE,OAAO,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;IAC5C,CAAC;IAED,cAAc;IACd,iDAAqB,GAArB;QACE,OAAO,IAAI,CAAC,UAAU,CAAC,uBAAuB,CAAC,CAAC;IAClD,CAAC;IAED,cAAc;IACd,qCAAS,GAAT;QACE,OAAO,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;IACtC,CAAC;IAED,cAAc;IACd,wCAAY,GAAZ;QACE,OAAO,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;IACzC,CAAC;IAED,cAAc;IACd,kCAAM,GAAN;QACE,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IACnC,CAAC;IAED,cAAc;IACd,6CAAiB,GAAjB;QACE,OAAO,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC;IAC9C,CAAC;IAED,cAAc;IACd,0CAAc,GAAd;QACE,IAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;QACtE,IAAI,cAAc,YAAY,QAAQ;YAAE,OAAO,cAAc,CAAC;QAC9D,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,cAAc;IACd,qCAAS,GAAT;QACE,IAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;QAC5D,IAAI,SAAS,YAAY,SAAS;YAAE,OAAO,SAAS,CAAC;QACrD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;;;OAIG;IACH,0CAAc,GAAd;;QACE,mBAAO,IAAI,CAAC,WAAW,EAAE,0CAAE,SAAS,qCAAM,KAAK,CAAC;IAClD,CAAC;IAED;;;;OAIG;IACH,0CAAc,GAAd;;QACE,mBAAO,IAAI,CAAC,WAAW,EAAE,0CAAE,SAAS,qCAAM,KAAK,CAAC;IAClD,CAAC;IAED;;;;;OAKG;IACH,2CAAe,GAAf;;QACE,mBAAO,IAAI,CAAC,YAAY,EAAE,0CAAE,SAAS,qCAAM,KAAK,CAAC;IACnD,CAAC;IAED;;;;OAIG;IACH,wCAAY,GAAZ;;QACE,mBAAO,IAAI,CAAC,SAAS,EAAE,0CAAE,SAAS,qCAAM,KAAK,CAAC;IAChD,CAAC;IAED;;;;OAIG;IACH,2CAAe,GAAf;;QACE,mBAAO,IAAI,CAAC,YAAY,EAAE,0CAAE,SAAS,qCAAM,KAAK,CAAC;IACnD,CAAC;IAED;;;;;;OAMG;IACH,8CAAkB,GAAlB;;QACE,mBAAO,IAAI,CAAC,eAAe,EAAE,0CAAE,SAAS,qCAAM,KAAK,CAAC;IACtD,CAAC;IAED;;;;OAIG;IACH,oDAAwB,GAAxB;;QACE,IAAM,IAAI,SAAG,IAAI,CAAC,qBAAqB,EAAE,0CAAE,UAAU,EAAE,CAAC;QACxD,aAAO,MAAM,CAAC,IAAI,EAAE,qBAAqB,CAAC,mCAAI,qBAAqB,CAAC,OAAO,CAAC;IAC9E,CAAC;IAED;;;OAGG;IACH,+CAAmB,GAAnB;;QACE,IAAM,SAAS,SAAG,IAAI,CAAC,SAAS,EAAE,0CAAE,UAAU,EAAE,CAAC;QACjD,aAAO,MAAM,CAAC,SAAS,EAAE,gBAAgB,CAAC,mCAAI,gBAAgB,CAAC,GAAG,CAAC;IACrE,CAAC;IAED;;;;OAIG;IACH,2CAAe,GAAf;;QACE,IAAM,OAAO,SAAG,IAAI,CAAC,YAAY,EAAE,0CAAE,UAAU,EAAE,CAAC;QAClD,aAAO,MAAM,CAAC,OAAO,EAAE,YAAY,CAAC,mCAAI,YAAY,CAAC,UAAU,CAAC;IAClE,CAAC;IAED;;;;OAIG;IACH,qCAAS,GAAT;;QACE,IAAM,MAAM,SAAG,IAAI,CAAC,MAAM,EAAE,0CAAE,UAAU,EAAE,CAAC;QAC3C,OAAO,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAChC,CAAC;IAED;;;;;OAKG;IACH,gDAAoB,GAApB;;QACE,aAAO,IAAI,CAAC,iBAAiB,EAAE,0CAAE,SAAS,GAAG;IAC/C,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACH,6CAAiB,GAAjB;QACE,IAAM,GAAG,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAClC,IAAI,CAAC,GAAG;YAAE,OAAO,EAAE,CAAC;QAEpB,IAAM,UAAU,GAAgB,EAAE,CAAC;QACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE;YACtC,wEAAwE;YACxE,kEAAkE;YAClE,yEAAyE;YACzE,4BAA4B;YAC5B,IAAM,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC;YAClD,IAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC;YACpD,UAAU,CAAC,IAAI,CAAC,EAAE,KAAK,OAAA,EAAE,GAAG,KAAA,EAAE,CAAC,CAAC;SACjC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;;;OAIG;IACH,wCAAY,GAAZ;;QACE,mBAAO,IAAI,CAAC,SAAS,EAAE,0CAAE,QAAQ,qCAAM,CAAC,CAAC;IAC3C,CAAC;IAED;;;;OAIG;IACH,0CAAc,GAAd,UAAe,WAAoB;QACjC,IAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACvD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,aAAa,CAAC,EAAE,WAAW,CAAC,CAAC;IACxD,CAAC;IAED;;;;OAIG;IACH,0CAAc,GAAd,UAAe,WAAoB;QACjC,IAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACvD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,aAAa,CAAC,EAAE,WAAW,CAAC,CAAC;IACxD,CAAC;IAED;;;;;OAKG;IACH,2CAAe,GAAf,UAAgB,YAAqB;QACnC,IAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QACzD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,cAAc,CAAC,EAAE,YAAY,CAAC,CAAC;IAC1D,CAAC;IAED;;;;OAIG;IACH,wCAAY,GAAZ,UAAa,SAAkB;QAC7B,IAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACnD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC,EAAE,SAAS,CAAC,CAAC;IACpD,CAAC;IAED;;;;OAIG;IACH,2CAAe,GAAf,UAAgB,YAAqB;QACnC,IAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QACzD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,cAAc,CAAC,EAAE,YAAY,CAAC,CAAC;IAC1D,CAAC;IAED;;;;;OAKG;IACH,8CAAkB,GAAlB,UAAmB,YAAqB;QACtC,IAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAC5D,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,iBAAiB,CAAC,EAAE,eAAe,CAAC,CAAC;IAChE,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,oDAAwB,GAAxB,UAAyB,qBAA4C;QACnE,aAAa,CACX,qBAAqB,EACrB,uBAAuB,EACvB,qBAAqB,CACtB,CAAC;QACF,IAAM,IAAI,GAAG,OAAO,CAAC,EAAE,CAAC,qBAAqB,CAAC,CAAC;QAC/C,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,uBAAuB,CAAC,EAAE,IAAI,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACH,+CAAmB,GAAnB,UAAoB,gBAAkC;QACpD,aAAa,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;QACtE,IAAM,SAAS,GAAG,OAAO,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC;QAC/C,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC,EAAE,SAAS,CAAC,CAAC;IACpD,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,2CAAe,GAAf,UAAgB,YAA0B;QACxC,aAAa,CAAC,YAAY,EAAE,cAAc,EAAE,YAAY,CAAC,CAAC;QAC1D,IAAM,OAAO,GAAG,OAAO,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;QACzC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,cAAc,CAAC,EAAE,OAAO,CAAC,CAAC;IACrD,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,qCAAS,GAAT,UAAU,MAAc;QACtB,aAAa,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;QACxC,IAAM,GAAG,GAAG,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAC/B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,CAAC;IAC3C,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,gDAAoB,GAApB,UAAqB,iBAA0B;QAC7C,IAAM,iBAAiB,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACnE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,mBAAmB,CAAC,EAAE,iBAAiB,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;OAyBG;IACH,6CAAiB,GAAjB,UAAkB,cAAuC;QACvD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC;YAAE,cAAc,GAAG,CAAC,cAAc,CAAC,CAAC;QAEtE,IAAM,SAAS,GAAa,EAAE,CAAC;QAC/B,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,cAAc,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YAC/D,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;YAC1C,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;SACzC;QAED,YAAY,CAAC,SAAS,EAAE,gBAAgB,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QAEtD,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,gBAAgB,CAAC,EAAE,UAAU,CAAC,CAAC;IAC1D,CAAC;IAED;;;;OAIG;IACH,wCAAY,GAAZ,UAAa,SAAiB;QAC5B,WAAW,CAAC,SAAS,EAAE,WAAW,EAAE,CAAC,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QACzD,aAAa,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;QACtC,IAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACnD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC,EAAE,SAAS,CAAC,CAAC;IACpD,CAAC;IA3dD,cAAc;IACP,0BAAQ,GAAG,UAAC,IAAa;QAC9B,OAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC;IAA3B,CAA2B,CAAC;IAE9B,cAAc;IACP,wBAAM,GAAG,UAAC,OAAmB;QAClC,IAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC7B,OAAO,IAAI,iBAAiB,CAAC,IAAI,CAAC,CAAC;IACrC,CAAC,CAAC;IAodJ,wBAAC;CAAA,AAheD,IAgeC;AAED,eAAe,iBAAiB,CAAC"}