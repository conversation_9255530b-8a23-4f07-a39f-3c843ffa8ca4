{"version": 3, "file": "PDFOperatorNames.js", "sourceRoot": "", "sources": ["../../../src/core/operators/PDFOperatorNames.ts"], "names": [], "mappings": "AAAA,IAAK,gBAyFJ;AAzFD,WAAK,gBAAgB;IACnB,+BAA+B;IAC/B,2CAAuB,CAAA;IACvB,6CAAyB,CAAA;IACzB,8CAA0B,CAAA;IAC1B,8CAA0B,CAAA;IAC1B,8CAA0B,CAAA;IAC1B,gDAA4B,CAAA;IAE5B,2BAA2B;IAC3B,wCAAoB,CAAA;IACpB,0CAAsB,CAAA;IACtB,2CAAuB,CAAA;IACvB,2CAAuB,CAAA;IACvB,2CAAuB,CAAA;IACvB,6CAAyB,CAAA;IAEzB,2BAA2B;IAC3B,sDAAkC,CAAA;IAClC,8CAA0B,CAAA;IAC1B,4CAAwB,CAAA;IACxB,sDAAkC,CAAA;IAClC,6CAAyB,CAAA;IACzB,qCAAiB,CAAA;IAEjB,2BAA2B;IAC3B,qDAAiC,CAAA;IACjC,0CAAsB,CAAA;IACtB,2CAAuB,CAAA;IACvB,qCAAiB,CAAA;IACjB,iDAA6B,CAAA;IAC7B,yCAAqB,CAAA;IACrB,4CAAwB,CAAA;IACxB,0CAAsB,CAAA;IACtB,2CAAuB,CAAA;IACvB,sCAAkB,CAAA;IAClB,wCAAoB,CAAA;IACpB,6CAAyB,CAAA;IAEzB,qBAAqB;IACrB,0CAAsB,CAAA;IACtB,2CAAuB,CAAA;IACvB,+CAA2B,CAAA;IAC3B,yCAAqB,CAAA;IACrB,sCAAkB,CAAA;IAClB,qCAAiB,CAAA;IACjB,wCAAoB,CAAA;IACpB,oDAAgC,CAAA;IAChC,mDAA+B,CAAA;IAC/B,mCAAe,CAAA;IACf,2CAAuB,CAAA;IACvB,oDAAgC,CAAA;IAChC,sDAAkC,CAAA;IAClC,iCAAa,CAAA;IACb,+CAA2B,CAAA;IAC3B,sCAAkB,CAAA;IAClB,8CAA0B,CAAA;IAC1B,qCAAiB,CAAA;IACjB,2CAAuB,CAAA;IACvB,gCAAY,CAAA;IACZ,gCAAY,CAAA;IACZ,sCAAkB,CAAA;IAClB,oCAAgB,CAAA;IAEhB,iBAAiB;IACjB,oCAAgB,CAAA;IAChB,kCAAc,CAAA;IACd,mCAAe,CAAA;IACf,6CAAyB,CAAA;IACzB,mCAAe,CAAA;IACf,8CAA0B,CAAA;IAC1B,yCAAqB,CAAA;IACrB,mDAA+B,CAAA;IAC/B,4CAAwB,CAAA;IACxB,+CAA2B,CAAA;IAC3B,sCAAkB,CAAA;IAClB,yCAAqB,CAAA;IACrB,mCAAe,CAAA;IACf,2CAAuB,CAAA;IACvB,sCAAkB,CAAA;IAClB,+CAA0B,CAAA;IAE1B,uBAAuB;IACvB,kCAAc,CAAA;IACd,kCAAc,CAAA;IAEd,kCAAkC;IAClC,oDAAgC,CAAA;IAChC,kDAA8B,CAAA;AAChC,CAAC,EAzFI,gBAAgB,KAAhB,gBAAgB,QAyFpB;AAED,eAAe,gBAAgB,CAAC"}