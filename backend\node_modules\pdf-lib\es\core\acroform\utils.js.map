{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../src/core/acroform/utils.ts"], "names": [], "mappings": "AACA,OAAO,SAAS,6BAAmC;AACnD,OAAO,OAAO,2BAAiC;AAC/C,OAAO,OAAO,2BAAiC;AAC/C,OAAO,QAAQ,4BAAkC;AACjD,OAAO,MAAM,0BAAgC;AAG7C,OAAO,eAAe,0BAA0C;AAChE,OAAO,kBAAkB,6BAA6C;AAEtE,OAAO,gBAAgB,2BAA2C;AAElE,OAAO,WAAW,sBAAsC;AACxD,OAAO,iBAAiB,4BAA4C;AACpE,OAAO,kBAAkB,6BAA6C;AACtE,OAAO,eAAe,0BAA0C;AAChE,OAAO,eAAe,0BAA0C;AAChE,OAAO,cAAc,yBAAyC;AAC9D,OAAO,EAAE,eAAe,EAAE,eAAe,EAAE,gBAAgC;AAE3E,MAAM,CAAC,IAAM,mBAAmB,GAAG,UACjC,QAAmB;IAEnB,IAAI,CAAC,QAAQ;QAAE,OAAO,EAAE,CAAC;IAEzB,IAAM,IAAI,GAA6B,EAAE,CAAC;IAC1C,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;QACzD,IAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAClC,uEAAuE;QACvE,IAAI,GAAG,YAAY,MAAM,IAAI,IAAI,YAAY,OAAO,EAAE;YACpD,IAAI,CAAC,IAAI,CAAC,CAAC,kBAAkB,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;SACjD;KACF;IAED,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,MAAM,CAAC,IAAM,kBAAkB,GAAG,UAChC,IAAa,EACb,GAAW;IAEX,IAAM,aAAa,GAAG,sBAAsB,CAAC,IAAI,CAAC,CAAC;IACnD,IAAI,aAAa;QAAE,OAAO,kBAAkB,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IACjE,OAAO,qBAAqB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAC1C,CAAC,CAAC;AAEF,8EAA8E;AAE9E,6BAA6B;AAC7B,EAAE;AACF,8EAA8E;AAC9E,6EAA6E;AAC7E,4EAA4E;AAC5E,2DAA2D;AAC3D,EAAE;AACF,wEAAwE;AACxE,+EAA+E;AAC/E,2EAA2E;AAC3E,8EAA8E;AAC9E,4EAA4E;AAC5E,+EAA+E;AAC/E,gCAAgC;AAChC,IAAM,sBAAsB,GAAG,UAAC,IAAa;IAC3C,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAE7C,IAAI,IAAI,YAAY,QAAQ,EAAE;QAC5B,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,IAAI,EAAE,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YACrD,IAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAC7B,IAAM,UAAU,GAAG,GAAG,YAAY,OAAO,IAAI,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;YACtE,IAAI,UAAU;gBAAE,OAAO,IAAI,CAAC;SAC7B;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAEF,IAAM,qBAAqB,GAAG,UAAC,IAAa,EAAE,GAAW;IACvD,IAAM,WAAW,GAAG,uBAAuB,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IACpE,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;IAEvD,IAAI,IAAI,KAAK,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC;QAAE,OAAO,mBAAmB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IACtE,IAAI,IAAI,KAAK,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC;QAAE,OAAO,mBAAmB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IACrE,IAAI,IAAI,KAAK,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC;QAAE,OAAO,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IACtE,IAAI,IAAI,KAAK,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC;QAAE,OAAO,gBAAgB,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IAE5E,yEAAyE;IACzE,4EAA4E;IAC5E,wBAAwB;IACxB,OAAO,eAAe,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAC7C,CAAC,CAAC;AAEF,IAAM,mBAAmB,GAAG,UAAC,IAAa,EAAE,GAAW;;IACrD,IAAM,aAAa,GAAG,uBAAuB,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IACtE,IAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;IACpE,IAAM,KAAK,SAAG,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,QAAQ,qCAAM,CAAC,CAAC;IAExC,IAAI,SAAS,CAAC,KAAK,EAAE,eAAe,CAAC,UAAU,CAAC,EAAE;QAChD,OAAO,iBAAiB,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;KAC9C;SAAM,IAAI,SAAS,CAAC,KAAK,EAAE,eAAe,CAAC,KAAK,CAAC,EAAE;QAClD,OAAO,kBAAkB,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;KAC/C;SAAM;QACL,OAAO,eAAe,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;KAC5C;AACH,CAAC,CAAC;AAEF,IAAM,mBAAmB,GAAG,UAAC,IAAa,EAAE,GAAW;;IACrD,IAAM,aAAa,GAAG,uBAAuB,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IACtE,IAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;IACpE,IAAM,KAAK,SAAG,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,QAAQ,qCAAM,CAAC,CAAC;IAExC,IAAI,SAAS,CAAC,KAAK,EAAE,eAAe,CAAC,KAAK,CAAC,EAAE;QAC3C,OAAO,eAAe,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;KAC5C;SAAM;QACL,OAAO,cAAc,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;KAC3C;AACH,CAAC,CAAC;AAEF,IAAM,SAAS,GAAG,UAAC,KAAa,EAAE,IAAY;IAC5C,OAAA,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAApB,CAAoB,CAAC;AAEvB,IAAM,uBAAuB,GAAG,UAAC,SAAkB,EAAE,IAAa;IAChE,IAAI,SAAgC,CAAC;IACrC,MAAM,CAAC,SAAS,EAAE,UAAC,IAAI;QACrB,IAAI,CAAC,SAAS;YAAE,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC,CAAC,CAAC;IACH,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AAEF,IAAM,MAAM,GAAG,UAAC,SAAkB,EAAE,OAA+B;IACjE,OAAO,CAAC,SAAS,CAAC,CAAC;IACnB,IAAM,MAAM,GAAG,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC,CAAC;IACpE,IAAI,MAAM;QAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AACtC,CAAC,CAAC"}