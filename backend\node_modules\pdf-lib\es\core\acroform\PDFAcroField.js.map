{"version": 3, "file": "PDFAcroField.js", "sourceRoot": "", "sources": ["../../../src/core/acroform/PDFAcroField.ts"], "names": [], "mappings": "AAAA,OAAO,OAAO,2BAAiC;AAC/C,OAAO,SAAS,6BAAmC;AACnD,OAAO,YAAY,gCAAsC;AACzD,OAAO,OAAO,2BAAiC;AAE/C,OAAO,SAAS,6BAAmC;AACnD,OAAO,QAAQ,4BAAkC;AACjD,OAAO,MAAM,0BAAgC;AAC7C,OAAO,EAAE,aAAa,EAAE,oBAAkB;AAC1C,OAAO,EAAE,mBAAmB,EAAE,sBAAsB,EAAE,kBAAwB;AAE9E,YAAY;AACZ,oCAAoC;AACpC,wCAAwC;AACxC,sCAAsC;AACtC,IAAM,OAAO,GAAG,qEAAqE,CAAC;AAEtF;IAIE,sBAAsB,IAAa,EAAE,GAAW;QAC9C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;IACjB,CAAC;IAED,wBAAC,GAAD;QACE,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;IACzE,CAAC;IAED,yBAAE,GAAF;QACE,IAAM,WAAW,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;QACnE,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;IAC/D,CAAC;IAED,wBAAC,GAAD;QACE,IAAM,UAAU,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QACjE,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;IAC9C,CAAC;IAED,2BAAI,GAAJ;QACE,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC,CAAC;IAC7D,CAAC;IAED,kCAAkC;IAClC,iEAAiE;IACjE,IAAI;IAEJ,yBAAE,GAAF;QACE,IAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;QAC9C,IAAI,EAAE,YAAY,SAAS,IAAI,EAAE,YAAY,YAAY;YAAE,OAAO,EAAE,CAAC;QACrE,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,8BAAO,GAAP,UAAQ,IAAiB;QACvB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;IACjE,CAAC;IAED,gCAAS,GAAT;QACE,gCAAgC;QAChC,iCAAiC;QACjC,mCAAmC;QAEnC,IAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QACtD,IAAI,SAAS,YAAY,MAAM,EAAE;YAC/B,IAAM,QAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC,CAAC;YAC/D,OAAO,IAAI,YAAY,CAAC,QAAM,EAAE,SAAS,CAAC,CAAC;SAC5C;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,gCAAS,GAAT,UAAU,MAA0B;QAClC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;;YAC/C,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,CAAC;IACnD,CAAC;IAED,4CAAqB,GAArB;QACE,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,IAAI,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC;QAC1C,OAAU,MAAM,CAAC,qBAAqB,EAAE,SAAI,IAAI,CAAC,cAAc,EAAI,CAAC;IACtE,CAAC;IAED,qCAAc,GAAd;;QACE,aAAO,IAAI,CAAC,CAAC,EAAE,0CAAE,UAAU,GAAG;IAChC,CAAC;IAED,qCAAc,GAAd,UAAe,WAA+B;QAC5C,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;;YAC/C,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;IAC1E,CAAC;IAED,2CAAoB,GAApB,UAAqB,UAAkB;QACrC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;IAC5D,CAAC;IAED,2CAAoB,GAApB;QACE,IAAM,EAAE,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;QAErB,IAAI,EAAE,YAAY,YAAY,EAAE;YAC9B,OAAO,EAAE,CAAC,UAAU,EAAE,CAAC;SACxB;QAED,OAAO,EAAE,aAAF,EAAE,uBAAF,EAAE,CAAE,QAAQ,GAAG;IACxB,CAAC;IAED,kCAAW,GAAX,UAAY,QAAgB;;QAC1B,IAAM,IAAI,SAAG,IAAI,CAAC,qBAAqB,EAAE,mCAAI,EAAE,CAAC;QAEhD,IAAM,EAAE,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QACvC,IAAI,CAAC,EAAE;YAAE,MAAM,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;QAE7C,IAAM,OAAO,GAAG,aAAa,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QAC3C,IAAI,CAAC,OAAO,CAAC,KAAK;YAAE,MAAM,IAAI,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAE3D,IAAM,OAAO,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QACnE,IAAM,KAAK,GAAG,OAAO,CAAC,GAAG,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACpE,IAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAClC,IAAM,UAAU,GAAM,OAAO,UAAK,QAAQ,SAAI,QAAQ,YAAO,KAAO,CAAC;QAErE,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;IACxC,CAAC;IAED,+BAAQ,GAAR;;QACE,mBAAO,IAAI,CAAC,EAAE,EAAE,0CAAE,QAAQ,qCAAM,CAAC,CAAC;IACpC,CAAC;IAED,+BAAQ,GAAR,UAAS,KAAa;QACpB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IACvD,CAAC;IAED,8BAAO,GAAP,UAAQ,IAAY;QAClB,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;IAC9B,CAAC;IAED,8BAAO,GAAP,UAAQ,IAAY;QAClB,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED,gCAAS,GAAT,UAAU,IAAY;QACpB,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED,gCAAS,GAAT,UAAU,IAAY,EAAE,MAAe;QACrC,IAAI,MAAM;YAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;;YAC1B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED,8CAAuB,GAAvB,UAAwB,IAAa;QACnC,IAAI,SAAgC,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,UAAC,IAAI;YACf,IAAI,CAAC,SAAS;gBAAE,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QACH,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,6BAAM,GAAN,UAAO,OAAoC;QACzC,OAAO,CAAC,IAAI,CAAC,CAAC;QACd,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,IAAI,MAAM;YAAE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACrC,CAAC;IACH,mBAAC;AAAD,CAAC,AAnJD,IAmJC;AAED,eAAe,YAAY,CAAC"}