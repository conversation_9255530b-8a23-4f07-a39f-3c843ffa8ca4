{"version": 3, "file": "PDFPageTree.js", "sourceRoot": "", "sources": ["../../../src/core/structures/PDFPageTree.ts"], "names": [], "mappings": ";AAAA,OAAO,QAAQ,4BAAkC;AACjD,OAAO,OAAoB,2BAAiC;AAC5D,OAAO,OAAO,2BAAiC;AAC/C,OAAO,SAAS,6BAAmC;AAGnD,OAAO,WAAW,sBAAwC;AAC1D,OAAO,EAAE,uBAAuB,EAAE,oBAAoB,EAAE,kBAAwB;AAIhF;IAA0B,+BAAO;IAAjC;;IAqLA,CAAC;IAxKC,4BAAM,GAAN;QACE,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,CAA4B,CAAC;IACtE,CAAC;IAED,0BAAI,GAAJ;QACE,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC,CAAC;IACnD,CAAC;IAED,2BAAK,GAAL;QACE,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,SAAS,CAAC,CAAC;IACrD,CAAC;IAED,kCAAY,GAAZ,UAAa,OAAe;QAC1B,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QACzB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACrB,CAAC;IAED,kCAAY,GAAZ,UAAa,OAAe;QAC1B,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QACzB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,CAAC;IAC3C,CAAC;IAED;;;;;;;;OAQG;IACH,oCAAc,GAAd,UAAe,OAAe,EAAE,WAAmB;QACjD,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QACzB,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,CAAC;QAEtC,IAAI,WAAW,GAAG,KAAK,EAAE;YACvB,MAAM,IAAI,uBAAuB,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;SACvD;QAED,IAAI,yBAAyB,GAAG,WAAW,CAAC;QAC5C,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,IAAI,EAAE,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YACrD,IAAI,yBAAyB,KAAK,CAAC,EAAE;gBACnC,yBAAyB;gBACzB,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;gBACjC,OAAO,SAAS,CAAC;aAClB;YAED,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAW,CAAC;YACvC,IAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAExC,IAAI,GAAG,YAAY,WAAW,EAAE;gBAC9B,IAAI,GAAG,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,GAAG,yBAAyB,EAAE;oBACtD,SAAS;oBACT,OAAO,CACL,GAAG,CAAC,cAAc,CAAC,OAAO,EAAE,yBAAyB,CAAC,IAAI,MAAM,CACjE,CAAC;iBACH;qBAAM;oBACL,UAAU;oBACV,yBAAyB,IAAI,GAAG,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,CAAC;iBACrD;aACF;YAED,IAAI,GAAG,YAAY,WAAW,EAAE;gBAC9B,UAAU;gBACV,yBAAyB,IAAI,CAAC,CAAC;aAChC;SACF;QAED,IAAI,yBAAyB,KAAK,CAAC,EAAE;YACnC,oCAAoC;YACpC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,CAAC;YACzC,OAAO,SAAS,CAAC;SAClB;QAED,kDAAkD;QAClD,MAAM,IAAI,oBAAoB,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;IAChE,CAAC;IAED;;;;;;;OAOG;IACH,oCAAc,GAAd,UAAe,WAAmB,EAAE,KAAY;QAAZ,sBAAA,EAAA,YAAY;QAC9C,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QACzB,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,CAAC;QAEtC,IAAI,WAAW,IAAI,KAAK,EAAE;YACxB,MAAM,IAAI,uBAAuB,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;SACvD;QAED,IAAI,yBAAyB,GAAG,WAAW,CAAC;QAC5C,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,IAAI,EAAE,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YACrD,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAW,CAAC;YACvC,IAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAExC,IAAI,GAAG,YAAY,WAAW,EAAE;gBAC9B,IAAI,GAAG,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,GAAG,yBAAyB,EAAE;oBACtD,SAAS;oBACT,GAAG,CAAC,cAAc,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;oBACrD,IAAI,KAAK,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC;wBAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;oBACvD,OAAO;iBACR;qBAAM;oBACL,UAAU;oBACV,yBAAyB,IAAI,GAAG,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,CAAC;iBACrD;aACF;YAED,IAAI,GAAG,YAAY,WAAW,EAAE;gBAC9B,IAAI,yBAAyB,KAAK,CAAC,EAAE;oBACnC,yBAAyB;oBACzB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;oBACpB,OAAO;iBACR;qBAAM;oBACL,UAAU;oBACV,yBAAyB,IAAI,CAAC,CAAC;iBAChC;aACF;SACF;QAED,kDAAkD;QAClD,MAAM,IAAI,oBAAoB,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;IAChE,CAAC;IAED,4BAAM,GAAN,UAAO,OAAmC;QACxC,OAAO,CAAC,IAAI,CAAC,CAAC;QACd,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAC7B,IAAI,MAAM;YAAE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACrC,CAAC;IAED,wDAAwD;IACxD,8BAAQ,GAAR,UAAS,OAA6C;QACpD,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QACzB,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,IAAI,EAAE,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YACrD,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAW,CAAC;YACvC,IAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAa,CAAC;YACpD,IAAI,GAAG,YAAY,WAAW;gBAAE,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACtD,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;SACtB;IACH,CAAC;IAEO,mCAAa,GAArB,UAAsB,MAAc,EAAE,OAAe;QACnD,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QAEzB,IAAI,CAAC,MAAM,CAAC,UAAC,IAAI;YACf,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;YAC7C,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAC/B,CAAC;IAEO,+BAAS,GAAjB,UAAkB,MAAc;QAC9B,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QAEzB,IAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAChC,IAAI,GAAG,YAAY,WAAW,EAAE;YAC9B,IAAI,CAAC,MAAM,CAAC,UAAC,IAAI;gBACf,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;gBAC7C,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;SACJ;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACtB,CAAC;IAnLM,uBAAW,GAAG,UAAC,OAAmB,EAAE,MAAe;QACxD,IAAM,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;QAClD,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C,IAAI,MAAM;YAAE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,CAAC;QACnD,OAAO,IAAI,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC,CAAC;IAEK,8BAAkB,GAAG,UAAC,GAAY,EAAE,OAAmB;QAC5D,OAAA,IAAI,WAAW,CAAC,GAAG,EAAE,OAAO,CAAC;IAA7B,CAA6B,CAAC;IA0KlC,kBAAC;CAAA,AArLD,CAA0B,OAAO,GAqLhC;AAED,eAAe,WAAW,CAAC"}