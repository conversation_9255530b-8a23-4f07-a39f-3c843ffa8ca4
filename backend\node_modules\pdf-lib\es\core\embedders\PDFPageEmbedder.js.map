{"version": 3, "file": "PDFPageEmbedder.js", "sourceRoot": "", "sources": ["../../../src/core/embedders/PDFPageEmbedder.ts"], "names": [], "mappings": ";AAAA,OAAO,EACL,iCAAiC,EACjC,2BAA2B,GAC5B,kBAAwB;AAEzB,OAAO,SAAS,6BAAmC;AACnD,OAAO,YAAY,gCAAsC;AAEzD,OAAO,SAAS,6BAAmC;AAEnD,OAAO,EAAE,kBAAkB,EAAE,0BAAgC;AAC7D,OAAO,gBAAgB,uCAA6C;AAEpE,OAAO,SAAS,4BAAkC;AAElD,OAAO,EAAE,mBAAmB,EAAE,oBAAkB;AAwBhD,IAAM,mBAAmB,GAAG,UAAC,IAAiB;IAC5C,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;IAEjC,IAAM,KAAK,GACT,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,QAAQ,EAAE;QACxC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC;IAE3C,IAAM,MAAM,GACV,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,QAAQ,EAAE;QACxC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC;IAE3C,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC;AAC3D,CAAC,CAAC;AAEF,6EAA6E;AAC7E,0BAA0B;AAC1B,IAAM,yBAAyB,GAAG,UAChC,EAAmB,IACM,OAAA,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,EAAlC,CAAkC,CAAC;AAE9D;IAgBE,yBACE,IAAiB,EACjB,WAA6B,EAC7B,oBAA2C;QAE3C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QAEjB,IAAM,EAAE,GAAG,WAAW,aAAX,WAAW,cAAX,WAAW,GAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;QAEpD,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC;QAChC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC;QACjC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,oBAAoB,GACvB,oBAAoB,aAApB,oBAAoB,cAApB,oBAAoB,GAAI,yBAAyB,CAAC,EAAE,CAAC,CAAC;IAC1D,CAAC;IA7BY,mBAAG,GAAhB,UACE,IAAiB,EACjB,WAA6B,EAC7B,oBAA2C;;;gBAE3C,sBAAO,IAAI,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,oBAAoB,CAAC,EAAC;;;KACrE;IAyBK,0CAAgB,GAAtB,UAAuB,OAAmB,EAAE,GAAY;;;;gBAChD,KAA0B,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAArD,QAAQ,cAAA,EAAE,SAAS,eAAA,CAAmC;gBAE9D,IAAI,CAAC,QAAQ;oBAAE,MAAM,IAAI,iCAAiC,EAAE,CAAC;gBACvD,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;gBAEhD,KAA+B,IAAI,CAAC,WAAW,EAA7C,IAAI,UAAA,EAAE,MAAM,YAAA,EAAE,KAAK,WAAA,EAAE,GAAG,SAAA,CAAsB;gBAChD,OAAO,GAAG,OAAO,CAAC,WAAW,CAAC,eAAe,EAAE;oBACnD,IAAI,EAAE,SAAS;oBACf,OAAO,EAAE,MAAM;oBACf,QAAQ,EAAE,CAAC;oBACX,IAAI,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,CAAC;oBAChC,MAAM,EAAE,IAAI,CAAC,oBAAoB;oBACjC,SAAS,WAAA;iBACV,CAAC,CAAC;gBAEH,IAAI,GAAG,EAAE;oBACP,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;oBAC7B,sBAAO,GAAG,EAAC;iBACZ;qBAAM;oBACL,sBAAO,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAC;iBAClC;;;;KACF;IAED,qFAAqF;IACrF,6EAA6E;IACrE,wCAAc,GAAtB,UAAuB,QAAkB;QACvC,IAAM,OAAO,GAAG,UAAU,CAAC,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACjD,IAAM,eAAe,GAAiB,EAAE,CAAC;QAEzC,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YACzD,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAE/C,IAAI,OAAO,SAAY,CAAC;YACxB,IAAI,MAAM,YAAY,YAAY,EAAE;gBAClC,OAAO,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,CAAC;aAC/C;iBAAM,IAAI,MAAM,YAAY,gBAAgB,EAAE;gBAC7C,OAAO,GAAG,MAAM,CAAC,oBAAoB,EAAE,CAAC;aACzC;iBAAM;gBACL,MAAM,IAAI,2BAA2B,CAAC,MAAM,CAAC,CAAC;aAC/C;YAED,eAAe,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;SACxC;QAED,OAAO,mBAAmB,eAAI,eAAe,EAAE;IACjD,CAAC;IACH,sBAAC;AAAD,CAAC,AA/ED,IA+EC;AAED,eAAe,eAAe,CAAC"}