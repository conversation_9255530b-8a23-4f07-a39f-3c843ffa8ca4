.format-settings-page {
  padding: 2rem 0;
}

.format-settings-container {
  max-width: 900px;
  margin: 0 auto;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.format-settings-container h1 {
  text-align: center;
  margin-bottom: 2rem;
  color: #212529;
}

/* Document info section */
.document-info {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.document-info h3 {
  margin-bottom: 1rem;
  font-size: 1.25rem;
  color: #495057;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 0.9rem;
  color: #6c757d;
  margin-bottom: 0.25rem;
}

.info-value {
  font-weight: 500;
  color: #212529;
}

/* Settings sections */
.settings-section {
  margin-bottom: 2.5rem;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 1.5rem;
}

.settings-section h3 {
  margin-bottom: 1.5rem;
  font-size: 1.25rem;
  color: #212529;
  position: relative;
  padding-bottom: 0.5rem;
}

.settings-section h3:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background-color: #0d6efd;
}

/* Form elements */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #495057;
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.75rem;
  font-size: 1rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  transition: border-color 0.2s;
}

.form-control:focus {
  border-color: #0d6efd;
  outline: none;
}

.form-control:hover {
  border-color: #adb5bd;
}

.form-hint {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #6c757d;
}

/* Warning and info notes */
.bleed-warning, .color-note, .info-note {
  margin-top: 0.75rem;
  padding: 0.75rem;
  border-radius: 4px;
  font-size: 0.9rem;
}

.bleed-warning {
  background-color: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  color: #856404;
}

.color-note {
  background-color: rgba(13, 202, 240, 0.1);
  border: 1px solid rgba(13, 202, 240, 0.3);
  color: #0c5460;
}

.info-note {
  background-color: rgba(108, 117, 125, 0.1);
  border: 1px solid rgba(108, 117, 125, 0.3);
  color: #495057;
}

.small-text {
  font-size: 0.85rem;
  margin-top: 0.5rem;
  margin-bottom: 0;
}

.note {
  margin: 0;
  margin-top: 0.75rem;
  padding: 0.75rem;
  border-radius: 4px;
  font-size: 0.9rem;
}

.bleed-warning {
  background-color: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  color: #856404;
}

.color-note {
  background-color: rgba(13, 202, 240, 0.1);
  border: 1px solid rgba(13, 202, 240, 0.3);
  color: #0c5460;
}

.info-note {
  background-color: rgba(108, 117, 125, 0.1);
  border: 1px solid rgba(108, 117, 125, 0.3);
  color: #495057;
}

.small-text {
  font-size: 0.85rem;
  margin-top: 0.5rem;
  margin-bottom: 0;
}



/* Checkbox styling */
.checkbox-group {
  display: flex;
  align-items: center;
}

.checkbox-group input[type="checkbox"] {
  margin-right: 0.75rem;
  width: 18px;
  height: 18px;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #495057;
  cursor: pointer;
}

/* Form rows for side-by-side controls */
.form-row {
  display: flex;
  gap: 1.5rem;
}

.form-row .form-group {
  flex: 1;
}

/* Margins grid */
.margins-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.margins-grid label {
  display: block;
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
  color: #6c757d;
}

.margins-grid input {
  width: 100%;
}

/* Form actions */
.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
}

.form-actions button {
  padding: 0.75rem 2rem;
}

/* Loading and error states */
.loading-container,
.error-container {
  text-align: center;
  padding: 3rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  margin-bottom: 1rem;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #0d6efd;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error-container h2 {
  color: #dc3545;
  margin-bottom: 1rem;
}

.error-container p {
  margin-bottom: 1.5rem;
  color: #6c757d;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .format-settings-container {
    padding: 1.5rem;
  }
  
  .form-row {
    flex-direction: column;
    gap: 1rem;
  }
  
  .margins-grid {
    grid-template-columns: 1fr;
  }
}