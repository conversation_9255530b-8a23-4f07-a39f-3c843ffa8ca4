{"version": 3, "file": "FileEmbedder.js", "sourceRoot": "", "sources": ["../../../src/core/embedders/FileEmbedder.ts"], "names": [], "mappings": ";AAAA,OAAO,SAAS,6BAAmC;AACnD,OAAO,YAAY,gCAAsC;AAIzD;;;;GAIG;AACH,MAAM,CAAN,IAAY,cASX;AATD,WAAY,cAAc;IACxB,mCAAiB,CAAA;IACjB,+BAAa,CAAA;IACb,6CAA2B,CAAA;IAC3B,2CAAyB,CAAA;IACzB,uDAAqC,CAAA;IACrC,+CAA6B,CAAA;IAC7B,mCAAiB,CAAA;IACjB,6CAA2B,CAAA;AAC7B,CAAC,EATW,cAAc,KAAd,cAAc,QASzB;AAUD;IAaE,sBACE,QAAoB,EACpB,QAAgB,EAChB,OAAiC;QAAjC,wBAAA,EAAA,YAAiC;QAEjC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IApBM,gBAAG,GAAV,UACE,KAAiB,EACjB,QAAgB,EAChB,OAAiC;QAAjC,wBAAA,EAAA,YAAiC;QAEjC,OAAO,IAAI,YAAY,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;IACpD,CAAC;IAgBK,uCAAgB,GAAtB,UAAuB,OAAmB,EAAE,GAAY;;;;gBAChD,KAMF,IAAI,CAAC,OAAO,EALd,QAAQ,cAAA,EACR,WAAW,iBAAA,EACX,YAAY,kBAAA,EACZ,gBAAgB,sBAAA,EAChB,cAAc,oBAAA,CACC;gBAEX,kBAAkB,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE;oBAC5D,IAAI,EAAE,cAAc;oBACpB,OAAO,EAAE,QAAQ,aAAR,QAAQ,cAAR,QAAQ,GAAI,SAAS;oBAC9B,MAAM,EAAE;wBACN,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM;wBAC1B,YAAY,EAAE,YAAY;4BACxB,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,YAAY,CAAC;4BAClC,CAAC,CAAC,SAAS;wBACb,OAAO,EAAE,gBAAgB;4BACvB,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,gBAAgB,CAAC;4BACtC,CAAC,CAAC,SAAS;qBACd;iBACF,CAAC,CAAC;gBACG,qBAAqB,GAAG,OAAO,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;gBAE7D,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC;oBAC/B,IAAI,EAAE,UAAU;oBAChB,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC;oBAC9B,EAAE,EAAE,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC;oBACxC,EAAE,EAAE,EAAE,CAAC,EAAE,qBAAqB,EAAE;oBAChC,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS;oBAClE,cAAc,EAAE,cAAc,aAAd,cAAc,cAAd,cAAc,GAAI,SAAS;iBAC5C,CAAC,CAAC;gBAEH,IAAI,GAAG,EAAE;oBACP,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;oBAClC,sBAAO,GAAG,EAAC;iBACZ;qBAAM;oBACL,sBAAO,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAC;iBACvC;;;;KACF;IACH,mBAAC;AAAD,CAAC,AA/DD,IA+DC;AAED,eAAe,YAAY,CAAC"}