{"version": 3, "file": "PDFCrossRefSection.js", "sourceRoot": "", "sources": ["../../../src/core/document/PDFCrossRefSection.ts"], "names": [], "mappings": "AAAA,OAAO,MAAM,0BAAgC;AAC7C,OAAO,SAAS,4BAAkC;AAClD,OAAO,EAAE,oBAAoB,EAAE,QAAQ,EAAE,oBAAkB;AAQ3D;;;GAGG;AACH;IAcE,4BAAoB,UAAwB;QAC1C,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACpD,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC;IAED,qCAAQ,GAAR,UAAS,GAAW,EAAE,MAAc;QAClC,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG,KAAA,EAAE,MAAM,QAAA,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;IAC/C,CAAC;IAED,4CAAe,GAAf,UAAgB,GAAW,EAAE,oBAA4B;QACvD,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG,KAAA,EAAE,MAAM,EAAE,oBAAoB,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;IACpE,CAAC;IAED,qCAAQ,GAAR;QACE,IAAI,OAAO,GAAG,QAAQ,CAAC;QAEvB,KACE,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EACpD,QAAQ,GAAG,QAAQ,EACnB,QAAQ,EAAE,EACV;YACA,IAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YACzC,OAAO,IAAO,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,YAAY,SAAI,KAAK,CAAC,MAAM,OAAI,CAAC;YAC5D,KACE,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,KAAK,CAAC,MAAM,EACzC,QAAQ,GAAG,QAAQ,EACnB,QAAQ,EAAE,EACV;gBACA,IAAM,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;gBAC9B,OAAO,IAAI,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;gBACnD,OAAO,IAAI,GAAG,CAAC;gBACf,OAAO,IAAI,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;gBAChE,OAAO,IAAI,GAAG,CAAC;gBACf,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;gBACrC,OAAO,IAAI,KAAK,CAAC;aAClB;SACF;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,wCAAW,GAAX;QACE,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YACjE,IAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YACzC,IAAM,gBAAgB,GAAG,UAAU,CAAC,MAAM,CAAC;YACpC,IAAA,UAAU,GAAI,UAAU,GAAd,CAAe;YAChC,IAAI,IAAI,CAAC,CAAC;YACV,IAAI,IAAI,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC;YACnD,IAAI,IAAI,MAAM,CAAC,gBAAgB,CAAC,CAAC,MAAM,CAAC;YACxC,IAAI,IAAI,EAAE,GAAG,gBAAgB,CAAC;SAC/B;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,0CAAa,GAAb,UAAc,MAAkB,EAAE,MAAc;QAC9C,IAAM,aAAa,GAAG,MAAM,CAAC;QAE7B,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;QAC/B,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;QAC/B,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;QAC/B,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;QAC/B,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC;QAErC,MAAM,IAAI,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QAE3E,OAAO,MAAM,GAAG,aAAa,CAAC;IAChC,CAAC;IAEO,sDAAyB,GAAjC,UACE,WAAsB,EACtB,MAAkB,EAClB,MAAc;QAEd,IAAM,aAAa,GAAG,MAAM,CAAC;QAC7B,IAAM,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;QAElC,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,MAAM,EAAE,GAAG,EAAE,EAAE;YACrC,IAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YAEzC,IAAM,iBAAiB,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YACjE,MAAM,IAAI,oBAAoB,CAAC,iBAAiB,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;YAClE,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC;YAEnC,IAAM,WAAW,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YAC9C,MAAM,IAAI,oBAAoB,CAAC,WAAW,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;YAC5D,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC;YAErC,MAAM,IAAI,IAAI,CAAC,qBAAqB,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;SAClE;QAED,OAAO,MAAM,GAAG,aAAa,CAAC;IAChC,CAAC;IAEO,kDAAqB,GAA7B,UACE,OAAgB,EAChB,MAAkB,EAClB,MAAc;QAEd,IAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAE9B,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,MAAM,EAAE,GAAG,EAAE,EAAE;YACrC,IAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;YAE3B,IAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;YAC5D,MAAM,IAAI,oBAAoB,CAAC,WAAW,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;YAC5D,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC;YAEnC,IAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;YACtE,MAAM,IAAI,oBAAoB,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;YACzD,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC;YAEnC,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YAE7D,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC;YACnC,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC;SACtC;QAED,OAAO,EAAE,GAAG,MAAM,CAAC;IACrB,CAAC;IAEO,mCAAM,GAAd,UAAe,SAAgB;QAC7B,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,EAAE;YAC1B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YACnC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;YAClB,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;YACrB,OAAO;SACR;QAED,IAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;QAE9C,IAAI,SAAS,CAAC,GAAG,CAAC,YAAY,GAAG,SAAS,CAAC,GAAG,CAAC,YAAY,GAAG,CAAC,EAAE;YAC/D,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YACnC,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;SACtB;aAAM;YACL,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACtB,IAAI,CAAC,WAAW,IAAI,CAAC,CAAC;SACvB;IACH,CAAC;IA1JM,yBAAM,GAAG;QACd,OAAA,IAAI,kBAAkB,CAAC;YACrB,GAAG,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;YACxB,MAAM,EAAE,CAAC;YACT,OAAO,EAAE,IAAI;SACd,CAAC;IAJF,CAIE,CAAC;IAEE,8BAAW,GAAG,cAAM,OAAA,IAAI,kBAAkB,EAAE,EAAxB,CAAwB,CAAC;IAoJtD,yBAAC;CAAA,AA5JD,IA4JC;AAED,eAAe,kBAAkB,CAAC"}