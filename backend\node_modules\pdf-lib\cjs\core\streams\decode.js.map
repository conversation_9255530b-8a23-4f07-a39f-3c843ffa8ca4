{"version": 3, "file": "decode.js", "sourceRoot": "", "sources": ["../../../src/core/streams/decode.ts"], "names": [], "mappings": ";;;;AAAA,oCAGyB;AACzB,yEAAiD;AACjD,uEAA+C;AAC/C,uEAA+C;AAE/C,2EAAmD;AAEnD,0EAA2D;AAC3D,4EAA6D;AAC7D,sEAAuD;AACvD,kEAAmD;AACnD,8EAA+D;AAC/D,4DAA6D;AAE7D,IAAM,YAAY,GAAG,UACnB,MAAkB,EAClB,QAAiB,EACjB,MAA4C;IAE5C,IAAI,QAAQ,KAAK,iBAAO,CAAC,EAAE,CAAC,aAAa,CAAC,EAAE;QAC1C,OAAO,IAAI,qBAAW,CAAC,MAAM,CAAC,CAAC;KAChC;IACD,IAAI,QAAQ,KAAK,iBAAO,CAAC,EAAE,CAAC,WAAW,CAAC,EAAE;QACxC,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,IAAI,MAAM,YAAY,iBAAO,EAAE;YAC7B,IAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,iBAAO,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC;YAC7D,IAAI,WAAW,YAAY,mBAAS,EAAE;gBACpC,WAAW,GAAG,WAAW,CAAC,QAAQ,EAAE,CAAC;aACtC;SACF;QACD,OAAO,IAAI,mBAAS,CAAC,MAAM,EAAE,SAAS,EAAE,WAAoB,CAAC,CAAC;KAC/D;IACD,IAAI,QAAQ,KAAK,iBAAO,CAAC,EAAE,CAAC,eAAe,CAAC,EAAE;QAC5C,OAAO,IAAI,uBAAa,CAAC,MAAM,CAAC,CAAC;KAClC;IACD,IAAI,QAAQ,KAAK,iBAAO,CAAC,EAAE,CAAC,gBAAgB,CAAC,EAAE;QAC7C,OAAO,IAAI,wBAAc,CAAC,MAAM,CAAC,CAAC;KACnC;IACD,IAAI,QAAQ,KAAK,iBAAO,CAAC,EAAE,CAAC,iBAAiB,CAAC,EAAE;QAC9C,OAAO,IAAI,yBAAe,CAAC,MAAM,CAAC,CAAC;KACpC;IACD,MAAM,IAAI,iCAAwB,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;AAC1D,CAAC,CAAC;AAEW,QAAA,kBAAkB,GAAG,UAAC,EAAgC;QAA9B,IAAI,UAAA,EAAE,QAAQ,cAAA;IACjD,IAAI,MAAM,GAAe,IAAI,gBAAM,CAAC,QAAQ,CAAC,CAAC;IAE9C,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IACjD,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAO,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC;IAE3D,IAAI,MAAM,YAAY,iBAAO,EAAE;QAC7B,MAAM,GAAG,YAAY,CACnB,MAAM,EACN,MAAM,EACN,WAAmD,CACpD,CAAC;KACH;SAAM,IAAI,MAAM,YAAY,kBAAQ,EAAE;QACrC,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,IAAI,EAAE,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YACvD,MAAM,GAAG,YAAY,CACnB,MAAM,EACN,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,iBAAO,CAAC,EAC3B,WAAW,IAAK,WAAwB,CAAC,WAAW,CAAC,GAAG,EAAE,iBAAO,CAAC,CACnE,CAAC;SACH;KACF;SAAM,IAAI,CAAC,CAAC,MAAM,EAAE;QACnB,MAAM,IAAI,kCAAyB,CAAC,CAAC,iBAAO,EAAE,kBAAQ,CAAC,EAAE,MAAM,CAAC,CAAC;KAClE;IAED,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC"}