[{"C:\\Users\\<USER>\\Desktop\\New folder\\My Project\\KDP\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\New folder\\My Project\\KDP\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\New folder\\My Project\\KDP\\frontend\\src\\pages\\FormatSettings.js": "3", "C:\\Users\\<USER>\\Desktop\\New folder\\My Project\\KDP\\frontend\\src\\pages\\Home.js": "4", "C:\\Users\\<USER>\\Desktop\\New folder\\My Project\\KDP\\frontend\\src\\pages\\Upload.js": "5", "C:\\Users\\<USER>\\Desktop\\New folder\\My Project\\KDP\\frontend\\src\\pages\\Generate.js": "6", "C:\\Users\\<USER>\\Desktop\\New folder\\My Project\\KDP\\frontend\\src\\pages\\Help.js": "7", "C:\\Users\\<USER>\\Desktop\\New folder\\My Project\\KDP\\frontend\\src\\pages\\Preview.js": "8", "C:\\Users\\<USER>\\Desktop\\New folder\\My Project\\KDP\\frontend\\src\\components\\layout\\Header.js": "9", "C:\\Users\\<USER>\\Desktop\\New folder\\My Project\\KDP\\frontend\\src\\components\\layout\\Footer.js": "10", "C:\\Users\\<USER>\\Desktop\\New folder\\My Project\\KDP\\frontend\\src\\components\\common\\Tooltip.js": "11"}, {"size": 263, "mtime": 1749531541405, "results": "12", "hashOfConfig": "13"}, {"size": 1105, "mtime": 1749531547600, "results": "14", "hashOfConfig": "13"}, {"size": 27903, "mtime": 1749533375206, "results": "15", "hashOfConfig": "13"}, {"size": 3593, "mtime": 1749531535884, "results": "16", "hashOfConfig": "13"}, {"size": 5992, "mtime": 1749531551859, "results": "17", "hashOfConfig": "13"}, {"size": 9675, "mtime": 1749531542469, "results": "18", "hashOfConfig": "13"}, {"size": 26516, "mtime": 1749537466946, "results": "19", "hashOfConfig": "13"}, {"size": 12631, "mtime": 1749531536805, "results": "20", "hashOfConfig": "13"}, {"size": 960, "mtime": 1749531554502, "results": "21", "hashOfConfig": "13"}, {"size": 1460, "mtime": 1749531552664, "results": "22", "hashOfConfig": "13"}, {"size": 820, "mtime": 1749531530454, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "13huc5g", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\New folder\\My Project\\KDP\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\New folder\\My Project\\KDP\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\New folder\\My Project\\KDP\\frontend\\src\\pages\\FormatSettings.js", ["57"], [], "C:\\Users\\<USER>\\Desktop\\New folder\\My Project\\KDP\\frontend\\src\\pages\\Home.js", [], [], "C:\\Users\\<USER>\\Desktop\\New folder\\My Project\\KDP\\frontend\\src\\pages\\Upload.js", [], [], "C:\\Users\\<USER>\\Desktop\\New folder\\My Project\\KDP\\frontend\\src\\pages\\Generate.js", [], [], "C:\\Users\\<USER>\\Desktop\\New folder\\My Project\\KDP\\frontend\\src\\pages\\Help.js", [], [], "C:\\Users\\<USER>\\Desktop\\New folder\\My Project\\KDP\\frontend\\src\\pages\\Preview.js", [], [], "C:\\Users\\<USER>\\Desktop\\New folder\\My Project\\KDP\\frontend\\src\\components\\layout\\Header.js", [], [], "C:\\Users\\<USER>\\Desktop\\New folder\\My Project\\KDP\\frontend\\src\\components\\layout\\Footer.js", [], [], "C:\\Users\\<USER>\\Desktop\\New folder\\My Project\\KDP\\frontend\\src\\components\\common\\Tooltip.js", [], [], {"ruleId": null, "fatal": true, "severity": 2, "message": "58", "line": 80, "column": 8, "nodeType": null}, "Parsing error: Identifier 'gutterSizeByPageCount' has already been declared. (80:8)"]