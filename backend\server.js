const express = require('express');
const cors = require('cors');
const path = require('path');
const bodyParser = require('body-parser');
const fs = require('fs');
require('dotenv').config();

// Import routes
const documentRoutes = require('./routes/document');
const validationRoutes = require('./routes/validation');
const conversionRoutes = require('./routes/conversion');

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Set up static directories
app.use('/api/previews', express.static(path.join(__dirname, 'public/previews')));
app.use('/api/downloads', express.static(path.join(__dirname, 'public/downloads')));

// API Routes
app.use('/api/document', documentRoutes);
app.use('/api/validation', validationRoutes);
app.use('/api/conversion', conversionRoutes);

// Basic status endpoint
app.get('/api/status', (req, res) => {
  res.json({ status: 'ok', version: '1.0.0' });
});

// Mock endpoints for previews and downloads
app.get('/api/previews/page-:pageNum.png', (req, res) => {
  // In a real app, we would generate or serve actual preview images
  // For this demo, we'll serve a placeholder image
  res.sendFile(path.join(__dirname, 'public/previews/placeholder.png'));
});

app.get('/api/previews/pdf-cover.png', (req, res) => {
  // Serve a mock PDF cover preview
  res.sendFile(path.join(__dirname, 'public/previews/pdf-cover.png'));
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: true,
    message: err.message || 'Something went wrong!',
    details: process.env.NODE_ENV === 'development' ? err.stack : {}
  });
});

// Create required directories if they don't exist
const directories = ['uploads', 'public', 'public/previews', 'public/downloads'];
directories.forEach(dir => {
  const dirPath = path.join(__dirname, dir);
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
});

// Create placeholder images for demo purposes
const placeholderImagePath = path.join(__dirname, 'public/previews/placeholder.png');
const pdfCoverPath = path.join(__dirname, 'public/previews/pdf-cover.png');

// In a real app, we would create actual images
// For this demo, we'll just check if they exist and log a message
if (!fs.existsSync(placeholderImagePath)) {
  console.log('Note: Placeholder images would be created here in a production app');
}

// Share document store between routes
// In a real app, this would be a database
const documents = {};
app.set('documents', documents);

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`API available at http://localhost:${PORT}/api`);
});