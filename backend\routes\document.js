const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { PDFDocument } = require('pdf-lib');
const mammoth = require('mammoth');

// Configure storage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    cb(null, `${Date.now()}-${file.originalname}`);
  }
});

// Create multer instance
const upload = multer({
  storage,
  limits: { fileSize: 50 * 1024 * 1024 }, // 50MB limit
  fileFilter: (req, file, cb) => {
    const allowedFileTypes = ['.docx', '.pdf', '.rtf', '.doc', '.txt'];
    const ext = path.extname(file.originalname).toLowerCase();
    
    if (allowedFileTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('Unsupported file format. Please upload DOCX, PDF, RTF, DOC, or TXT files.'));
    }
  }
});

// Store uploaded documents
const documents = {};

// Upload document
router.post('/upload', upload.single('manuscript'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const file = req.file;
    const fileId = Date.now().toString();
    let documentInfo = {
      id: fileId,
      fileName: file.originalname,
      filePath: file.path,
      fileSize: file.size,
      fileFormat: path.extname(file.originalname).substring(1).toUpperCase(),
      uploadDate: new Date(),
      pageCount: 0,
      wordCount: 0
    };

    // Extract additional document info based on file type
    try {
      if (file.originalname.endsWith('.pdf')) {
        // Process PDF
        const pdfBuffer = fs.readFileSync(file.path);
        const pdfDoc = await PDFDocument.load(pdfBuffer);
        documentInfo.pageCount = pdfDoc.getPageCount();
        // Approximate word count for PDFs
        documentInfo.wordCount = documentInfo.pageCount * 250; // Rough estimate
      } else if (file.originalname.endsWith('.docx')) {
        // Process DOCX
        const result = await mammoth.extractRawText({ path: file.path });
        const text = result.value;
        documentInfo.wordCount = text.split(/\s+/).length;
        // Approximate page count for DOCX
        documentInfo.pageCount = Math.ceil(documentInfo.wordCount / 250);
      } else {
        // For other formats, make approximations
        const stats = fs.statSync(file.path);
        const fileSizeInBytes = stats.size;
        documentInfo.wordCount = Math.ceil(fileSizeInBytes / 6); // Very rough estimate
        documentInfo.pageCount = Math.ceil(documentInfo.wordCount / 250);
      }
    } catch (err) {
      console.error('Error extracting document info:', err);
      // If we can't extract info, use defaults
      documentInfo.pageCount = 1;
      documentInfo.wordCount = 100;
    }

    // Generate recommended settings based on document analysis and KDP guidelines
    documentInfo.recommendedSettings = {
      trimSize: '6x9', // Most popular size per KDP guidelines (6"x9", i.e. 152.4×228.6 mm)
      pageColor: 'white', // Black & white is more affordable
      marginTop: 0.75, // Above minimum for better appearance
      marginBottom: 0.75, // Above minimum for better appearance
      marginInside: documentInfo.pageCount <= 150 ? 0.5 : // 0.375" minimum + extra for safety
                    documentInfo.pageCount <= 300 ? 0.625 : // 0.5" minimum + extra for safety
                    documentInfo.pageCount <= 500 ? 0.75 : // 0.625" minimum + extra for safety
                    documentInfo.pageCount <= 700 ? 0.875 : // 0.75" minimum + extra for safety
                    1.0, // 0.875" minimum + extra for safety for 701-828 pages
      marginOutside: 0.5, // Above 0.25" minimum for better appearance
      hasBleed: false, // Default to no bleed for text-only documents
      paperType: documentInfo.fileFormat.toUpperCase() === 'PDF' ? 'white' : 'cream', // Cream is popular for fiction
      pageNumbering: true, // Standard for most books
      pageNumberPosition: 'bottom-center', // Common standard position
      fontName: 'Times New Roman', // Classic book font
      fontSize: 11, // Good readable size that's not too large
      lineSpacing: 1.5, // Good readability spacing
      includeTOC: documentInfo.pageCount > 24, // Include TOC for books with enough pages
      includeChapterBreaks: true, // Professional standard
      includeSpineText: documentInfo.pageCount >= 79 // KDP only allows spine text for books with at least 79 pages
    };

    // Store document info
    documents[fileId] = documentInfo;

    res.status(200).json({
      documentId: fileId,
      message: 'File uploaded successfully'
    });
  } catch (err) {
    console.error('Upload error:', err);
    res.status(500).json({
      error: true,
      message: err.message || 'Error uploading file'
    });
  }
});

// Get document info
router.get('/:id', (req, res) => {
  const { id } = req.params;
  
  if (!documents[id]) {
    return res.status(404).json({
      error: true,
      message: 'Document not found'
    });
  }
  
  res.status(200).json(documents[id]);
});

// Save format settings
router.post('/:id/settings', (req, res) => {
  const { id } = req.params;
  const settings = req.body;
  
  if (!documents[id]) {
    return res.status(404).json({
      error: true,
      message: 'Document not found'
    });
  }
  
  // Store settings
  documents[id].settings = settings;
  const formatId = Date.now().toString();
  
  res.status(200).json({
    formatId,
    message: 'Settings saved successfully'
  });
});

// Get document preview
router.get('/:id/preview/:formatId', (req, res) => {
  const { id, formatId } = req.params;
  
  if (!documents[id]) {
    return res.status(404).json({
      error: true,
      message: 'Document not found'
    });
  }
  
  // In a real app, we would generate actual preview images here
  // For this demonstration, we'll return mock data
  res.status(200).json({
    documentId: id,
    formatId,
    totalPages: documents[id].pageCount,
    previewBaseUrl: '/api/previews', // Base URL for preview images
    settings: documents[id].settings
  });
});

module.exports = router;