{"version": 3, "file": "PDFDict.d.ts", "sourceRoot": "", "sources": ["../../../src/core/objects/PDFDict.ts"], "names": [], "mappings": "AAAA,OAAO,QAAQ,mBAAkC;AACjD,OAAO,OAAO,kBAAiC;AAC/C,OAAO,YAAY,uBAAsC;AACzD,OAAO,OAAO,kBAAiC;AAC/C,OAAO,OAAO,kBAAiC;AAC/C,OAAO,SAAS,oBAAmC;AACnD,OAAO,SAAS,oBAAmC;AACnD,OAAO,MAAM,iBAAgC;AAC7C,OAAO,SAAS,oBAAmC;AACnD,OAAO,SAAS,oBAAmC;AACnD,OAAO,UAAU,sBAA4B;AAG7C,oBAAY,OAAO,GAAG,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AAE9C,cAAM,OAAQ,SAAQ,SAAS;IAC7B,MAAM,CAAC,WAAW,YAAa,UAAU,aAAqC;IAE9E,MAAM,CAAC,kBAAkB,0BAA2B,UAAU,aAClC;IAE5B,QAAQ,CAAC,OAAO,EAAE,UAAU,CAAC;IAE7B,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAU;IAE/B,SAAS,aAAa,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU;IAMvD,IAAI,IAAI,OAAO,EAAE;IAIjB,MAAM,IAAI,SAAS,EAAE;IAIrB,OAAO,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,EAAE;IAIjC,GAAG,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,GAAG,IAAI;IAIzC,GAAG,CACD,GAAG,EAAE,OAAO,EAGZ,eAAe,UAAQ,GACtB,SAAS,GAAG,SAAS;IAMxB,GAAG,CAAC,GAAG,EAAE,OAAO,GAAG,OAAO;IAK1B,WAAW,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,QAAQ,GAAG,QAAQ,GAAG,SAAS;IACtE,WAAW,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,OAAO,GAAG,OAAO,GAAG,SAAS;IACpE,WAAW,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,OAAO,GAAG,OAAO,GAAG,SAAS;IACpE,WAAW,CACT,GAAG,EAAE,OAAO,EACZ,IAAI,EAAE,OAAO,YAAY,GACxB,YAAY,GAAG,SAAS;IAC3B,WAAW,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,OAAO,GAAG,OAAO,GAAG,SAAS;IACpE,WAAW,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,OAAO,GAAG,OAAO,OAAO,GAAG,SAAS;IAC3E,WAAW,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,SAAS,GAAG,SAAS,GAAG,SAAS;IACxE,WAAW,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,SAAS,GAAG,SAAS,GAAG,SAAS;IACxE,WAAW,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,MAAM,GAAG,MAAM,GAAG,SAAS;IAClE,WAAW,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,SAAS,GAAG,SAAS,GAAG,SAAS;IACxE,WAAW,CACT,GAAG,EAAE,OAAO,EACZ,KAAK,EAAE,OAAO,SAAS,EACvB,KAAK,EAAE,OAAO,YAAY,GACzB,SAAS,GAAG,YAAY,GAAG,SAAS;IACvC,WAAW,CACT,GAAG,EAAE,OAAO,EACZ,KAAK,EAAE,OAAO,OAAO,EACrB,KAAK,EAAE,OAAO,SAAS,GACtB,OAAO,GAAG,SAAS,GAAG,SAAS;IAClC,WAAW,CACT,GAAG,EAAE,OAAO,EACZ,KAAK,EAAE,OAAO,SAAS,EACvB,KAAK,EAAE,OAAO,YAAY,EAC1B,KAAK,EAAE,OAAO,QAAQ,GACrB,SAAS,GAAG,YAAY,GAAG,QAAQ,GAAG,SAAS;IAkBlD,MAAM,CAAC,GAAG,EAAE,OAAO,GAAG,SAAS,GAAG,SAAS;IAC3C,MAAM,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,QAAQ,GAAG,QAAQ;IACrD,MAAM,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,OAAO,GAAG,OAAO;IACnD,MAAM,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,OAAO,GAAG,OAAO;IACnD,MAAM,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,YAAY,GAAG,YAAY;IAC7D,MAAM,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,OAAO,GAAG,OAAO;IACnD,MAAM,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,OAAO,GAAG,OAAO,OAAO;IAC1D,MAAM,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,SAAS,GAAG,SAAS;IACvD,MAAM,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,SAAS,GAAG,SAAS;IACvD,MAAM,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,MAAM,GAAG,MAAM;IACjD,MAAM,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,SAAS,GAAG,SAAS;IACvD,MAAM,CACJ,GAAG,EAAE,OAAO,EACZ,KAAK,EAAE,OAAO,SAAS,EACvB,KAAK,EAAE,OAAO,YAAY,GACzB,SAAS,GAAG,YAAY;IAC3B,MAAM,CACJ,GAAG,EAAE,OAAO,EACZ,KAAK,EAAE,OAAO,OAAO,EACrB,KAAK,EAAE,OAAO,SAAS,GACtB,OAAO,GAAG,SAAS;IACtB,MAAM,CACJ,GAAG,EAAE,OAAO,EACZ,KAAK,EAAE,OAAO,SAAS,EACvB,KAAK,EAAE,OAAO,YAAY,EAC1B,KAAK,EAAE,OAAO,QAAQ,GACrB,SAAS,GAAG,YAAY,GAAG,QAAQ;IAkBtC,MAAM,CAAC,GAAG,EAAE,OAAO,GAAG,OAAO;IAI7B,KAAK,IAAI,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC;IAIhC,kEAAkE;IAClE,SAAS,CAAC,GAAG,SAAK,GAAG,OAAO;IAS5B,KAAK,CAAC,OAAO,CAAC,EAAE,UAAU,GAAG,OAAO;IAUpC,QAAQ,IAAI,MAAM;IAWlB,WAAW,IAAI,MAAM;IAUrB,aAAa,CAAC,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,GAAG,MAAM;CAqB1D;AAED,eAAe,OAAO,CAAC"}