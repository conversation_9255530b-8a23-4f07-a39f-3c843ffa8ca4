{"version": 3, "file": "PDFString.js", "sourceRoot": "", "sources": ["../../../src/core/objects/PDFString.ts"], "names": [], "mappings": ";AAAA,OAAO,SAAS,oBAAmC;AACnD,OAAO,SAAS,4BAAkC;AAClD,OAAO,EACL,oBAAoB,EACpB,QAAQ,EACR,WAAW,EACX,oBAAoB,EACpB,UAAU,EACV,SAAS,EACT,WAAW,GACZ,oBAAkB;AACnB,OAAO,EAAE,yBAAyB,EAAE,kBAAwB;AAE5D;IAAwB,6BAAS;IAkB/B,mBAAoB,KAAa;QAAjC,YACE,iBAAO,SAER;QADC,KAAI,CAAC,KAAK,GAAG,KAAK,CAAC;;IACrB,CAAC;IAED,2BAAO,GAAP;QACE,IAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,IAAI,KAAK,GAAG,EAAE,CAAC;QACf,IAAI,OAAO,GAAG,KAAK,CAAC;QAEpB,IAAM,QAAQ,GAAG,UAAC,IAAa;YAC7B,IAAI,IAAI,KAAK,SAAS;gBAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,OAAO,GAAG,KAAK,CAAC;QAClB,CAAC,CAAC;QAEF,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YAC3D,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC7B,IAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;YAC9B,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;YACrC,IAAI,CAAC,OAAO,EAAE;gBACZ,IAAI,IAAI,KAAK,SAAS,CAAC,SAAS;oBAAE,OAAO,GAAG,IAAI,CAAC;;oBAC5C,QAAQ,CAAC,IAAI,CAAC,CAAC;aACrB;iBAAM;gBACL,IAAI,IAAI,KAAK,SAAS,CAAC,OAAO;oBAAE,QAAQ,EAAE,CAAC;qBACtC,IAAI,IAAI,KAAK,SAAS,CAAC,cAAc;oBAAE,QAAQ,EAAE,CAAC;qBAClD,IAAI,IAAI,KAAK,SAAS,CAAC,CAAC;oBAAE,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;qBACtD,IAAI,IAAI,KAAK,SAAS,CAAC,CAAC;oBAAE,QAAQ,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;qBAC7D,IAAI,IAAI,KAAK,SAAS,CAAC,CAAC;oBAAE,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;qBAClD,IAAI,IAAI,KAAK,SAAS,CAAC,CAAC;oBAAE,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;qBACxD,IAAI,IAAI,KAAK,SAAS,CAAC,CAAC;oBAAE,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;qBACvD,IAAI,IAAI,KAAK,SAAS,CAAC,SAAS;oBAAE,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;qBAChE,IAAI,IAAI,KAAK,SAAS,CAAC,UAAU;oBAAE,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;qBAClE,IAAI,IAAI,KAAK,SAAS,CAAC,SAAS;oBAAE,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;qBAChE,IAAI,IAAI,IAAI,SAAS,CAAC,IAAI,IAAI,IAAI,IAAI,SAAS,CAAC,KAAK,EAAE;oBAC1D,KAAK,IAAI,IAAI,CAAC;oBACd,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,QAAQ,IAAI,GAAG,IAAI,QAAQ,IAAI,GAAG,CAAC,EAAE;wBAC/D,QAAQ,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;wBAC7B,KAAK,GAAG,EAAE,CAAC;qBACZ;iBACF;qBAAM;oBACL,QAAQ,CAAC,IAAI,CAAC,CAAC;iBAChB;aACF;SACF;QAED,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAED,8BAAU,GAAV;QACE,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,WAAW,CAAC,KAAK,CAAC;YAAE,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC;QAClD,OAAO,oBAAoB,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;IAED,8BAAU,GAAV;QACE,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAC/B,IAAM,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;QAC7B,IAAI,CAAC,IAAI;YAAE,MAAM,IAAI,yBAAyB,CAAC,IAAI,CAAC,CAAC;QACrD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,4BAAQ,GAAR;QACE,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED,yBAAK,GAAL;QACE,OAAO,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAClC,CAAC;IAED,4BAAQ,GAAR;QACE,OAAO,MAAI,IAAI,CAAC,KAAK,MAAG,CAAC;IAC3B,CAAC;IAED,+BAAW,GAAX;QACE,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;IAC/B,CAAC;IAED,iCAAa,GAAb,UAAc,MAAkB,EAAE,MAAc;QAC9C,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,CAAC,SAAS,CAAC;QACvC,MAAM,IAAI,oBAAoB,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QAC3D,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,CAAC,UAAU,CAAC;QACxC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;IAC/B,CAAC;IApGD,8EAA8E;IAC9E,6EAA6E;IAC7E,oDAAoD;IAC7C,YAAE,GAAG,UAAC,KAAa,IAAK,OAAA,IAAI,SAAS,CAAC,KAAK,CAAC,EAApB,CAAoB,CAAC;IAE7C,kBAAQ,GAAG,UAAC,IAAU;QAC3B,IAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QAC7D,IAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QAC/D,IAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QACxD,IAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QAC3D,IAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QAC5D,IAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QAC5D,OAAO,IAAI,SAAS,CAAC,OAAK,IAAI,GAAG,KAAK,GAAG,GAAG,GAAG,KAAK,GAAG,IAAI,GAAG,IAAI,MAAG,CAAC,CAAC;IACzE,CAAC,CAAC;IAwFJ,gBAAC;CAAA,AAtGD,CAAwB,SAAS,GAsGhC;AAED,eAAe,SAAS,CAAC"}