const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs');

// Reference to documents store (in a real app, this would be a database)
const documents = {}; // This should be shared with document.js

// KDP requirements based on official guidelines
const KDP_REQUIREMENTS = {
  minPageCount: 24, // KDP requires minimum 24 pages
  maxPageCount: {
    // Maximum page counts for black and white on white paper
    '5x8': 828, // Standard size
    '5.5x8.5': 828,
    '6x9': 828, // Standard size
    '7x10': 828,
    '8.5x11': 828,
    // For color books, these would be lower (typically 600 pages)
  },
  minFontSize: 7, // points - <PERSON><PERSON> recommended minimum
  minImageResolution: 300, // DPI - KDP recommended minimum
  maxImageResolution: 600, // DPI - Practical upper limit to avoid file size issues
  minMargins: {
    withoutBleed: {
      inside: 0.375, // inches - KDP standard bleed amount (gutter) - For 24-150 pages
      outside: 0.25, // inches - KDP minimum for non-bleed
      top: 0.25, // inches - KDP minimum for non-bleed
      bottom: 0.25 // inches - KDP minimum for non-bleed
    },
    withBleed: {
      inside: 0.375, // inches (gutter) - For 24-150 pages
      outside: 0.375, // inches - KDP minimum for bleed
      top: 0.375, // inches - KDP minimum for bleed
      bottom: 0.375 // inches - KDP minimum for bleed
    }
  },
  bleedAmount: 0.125, // inches - KDP standard bleed amount
  supportedTrimSizes: [
    { value: '5x8', name: '5" x 8"', width: 5, height: 8, description: 'Standard for textbooks and larger novels (KDP recommended)' },
    { value: '5.5x8.5', name: '5.5" x 8.5" (Letter)', width: 5.5, height: 8.5, description: 'Popular size for fiction and non-fiction' },
    { value: '6x9', name: '6" x 9"', width: 6, height: 9, description: 'Common for fiction, novellas, short non-fiction' },
    { value: '7x10', name: '7" x 10"', width: 7, height: 10, description: 'Good for workbooks and textbooks' },
    { value: '8.5x11', name: '8.5" x 11" (Letter)', width: 8.5, height: 11, description: 'Letter size, good for large-format books' }
    // KDP allows custom sizes within 4"–8.5" width and 6"–11.69" height
  ],
  paperTypes: ['white', 'cream'], // Cream only available for black & white interiors
  spineWidthCalculation: {
    whiteBlackAndWhite: 0.002252, // inches per page
    creamBlackAndWhite: 0.0025, // inches per page
    whiteStandardColor: 0.002252, // inches per page
    whitePremiumColor: 0.002347 // inches per page
  },
  // Gutter size increases with page count
  gutterSizeByPageCount: [
    { maxPages: 150, minGutterSize: 0.375 },
    { maxPages: 300, minGutterSize: 0.5 },
    { maxPages: 500, minGutterSize: 0.625 },
    { maxPages: 700, minGutterSize: 0.75 },
    { maxPages: 828, minGutterSize: 0.875 }
  ],
  minSpineTextPages: 79, // Minimum pages required to have text on spine
  maxFileSize: 650 * 1024 * 1024 // 650MB - KDP's maximum file size
};

// Get validation issues
router.get('/:id/issues', (req, res) => {
  const { id } = req.params;
  
  if (!documents[id]) {
    return res.status(404).json({
      error: true,
      message: 'Document not found'
    });
  }
  
  const document = documents[id];
  const settings = document.settings || {};
  
  // Perform validation checks based on KDP requirements
  const issues = validateDocument(document, settings);
  
  res.status(200).json({
    documentId: id,
    issues
  });
});

// Run validation
router.post('/:id/run', (req, res) => {
  const { id } = req.params;
  
  if (!documents[id]) {
    return res.status(404).json({
      error: true,
      message: 'Document not found'
    });
  }
  
  const document = documents[id];
  const settings = document.settings || {};
  
  // Perform more thorough validation
  const issues = validateDocument(document, settings, true);
  
  res.status(200).json({
    documentId: id,
    issues
  });
});

// Fix issue
router.post('/:id/fix/:issueId', (req, res) => {
  const { id, issueId } = req.params;
  
  if (!documents[id]) {
    return res.status(404).json({
      error: true,
      message: 'Document not found'
    });
  }
  
  const document = documents[id];
  const settings = document.settings || {};
  
  // In a real app, we would implement specific fixes for each issue type
  // For this demo, we'll just return a success message
  
  let message = 'Issue fixed successfully';
  
  // Example of how fixes might be implemented
  if (issueId.includes('margin')) {
    if (settings.hasBleed) {
      settings.marginInside = Math.max(settings.marginInside, KDP_REQUIREMENTS.minMargins.withBleed.inside);
      settings.marginOutside = Math.max(settings.marginOutside, KDP_REQUIREMENTS.minMargins.withBleed.outside);
      settings.marginTop = Math.max(settings.marginTop, KDP_REQUIREMENTS.minMargins.withBleed.top);
      settings.marginBottom = Math.max(settings.marginBottom, KDP_REQUIREMENTS.minMargins.withBleed.bottom);
    } else {
      settings.marginInside = Math.max(settings.marginInside, KDP_REQUIREMENTS.minMargins.withoutBleed.inside);
      settings.marginOutside = Math.max(settings.marginOutside, KDP_REQUIREMENTS.minMargins.withoutBleed.outside);
      settings.marginTop = Math.max(settings.marginTop, KDP_REQUIREMENTS.minMargins.withoutBleed.top);
      settings.marginBottom = Math.max(settings.marginBottom, KDP_REQUIREMENTS.minMargins.withoutBleed.bottom);
    }
    
    message = 'Margins have been adjusted to meet KDP requirements';
  } else if (issueId.includes('gutter')) {
    // Find required gutter size based on page count
    const pageCount = document.pageCount;
    let requiredGutterSize = 0.375; // Default
    
    for (const sizeRule of KDP_REQUIREMENTS.gutterSizeByPageCount) {
      if (pageCount <= sizeRule.maxPages) {
        requiredGutterSize = sizeRule.minGutterSize;
        break;
      }
    }
    
    settings.marginInside = Math.max(settings.marginInside, requiredGutterSize);
    message = `Inside margin (gutter) has been increased to ${requiredGutterSize}" to accommodate your ${pageCount}-page book`;
  } else if (issueId.includes('pageCount')) {
    message = 'Page count issues must be resolved by adding or removing content from your manuscript';
  }
  
  // Save the updated settings
  documents[id].settings = settings;
  
  res.status(200).json({
    documentId: id,
    issueId,
    message,
    updatedSettings: settings
  });
});

// Validation function based on KDP guidelines
function validateDocument(document, settings, thorough = false) {
  const issues = [];
  
  // Helper function to add issues
  const addIssue = (id, message, severity, location = null, canAutoFix = false, description = null) => {
    issues.push({
      id,
      message,
      severity, // 'error', 'warning', or 'info'
      location,
      canAutoFix,
      status: 'open',
      description
    });
  };
  
  // Page count validation
  const pageCount = document.pageCount;
  if (pageCount < KDP_REQUIREMENTS.minPageCount) {
    addIssue(
      'pageCount-min',
      `Your manuscript has only ${pageCount} pages. KDP requires a minimum of ${KDP_REQUIREMENTS.minPageCount} pages for paperbacks.`,
      'error',
      null,
      false,
      'You will need to add more content to your book to meet the minimum page requirement. KDP will round up to an even page count if needed.'
    );
  }
  
  const trimSize = settings.trimSize || '6x9';
  if (pageCount > KDP_REQUIREMENTS.maxPageCount[trimSize]) {
    addIssue(
      'pageCount-max',
      `Your manuscript has ${pageCount} pages, which exceeds the maximum of ${KDP_REQUIREMENTS.maxPageCount[trimSize]} pages for a ${trimSize} trim size.`,
      'error',
      null,
      false,
      'You will need to reduce your content, choose a larger trim size, or split your book into multiple volumes.'
    );
  }
  
  // Margin validation
  if (settings.hasBleed) {
    // Check margins with bleed (0.375" minimum)
    if (settings.marginInside < KDP_REQUIREMENTS.minMargins.withBleed.inside) {
      addIssue(
        'margin-inside-bleed',
        `Your inside margin is set to ${settings.marginInside}", but KDP requires at least ${KDP_REQUIREMENTS.minMargins.withBleed.inside}" for books with bleed.`,
        'error',
        null,
        true,
        'The inside margin (gutter) is the margin closest to the book binding. Adequate margins prevent text from disappearing into the binding. For books with bleed, KDP requires larger margins.'
      );
    }
    
    if (settings.marginOutside < KDP_REQUIREMENTS.minMargins.withBleed.outside) {
      addIssue(
        'margin-outside-bleed',
        `Your outside margin is set to ${settings.marginOutside}", but KDP requires at least ${KDP_REQUIREMENTS.minMargins.withBleed.outside}" for books with bleed.`,
        'error',
        null,
        true,
        'Outside margins that are too small may cause text to be cut off during printing. For books with bleed, KDP requires larger margins.'
      );
    }
    
    if (settings.marginTop < KDP_REQUIREMENTS.minMargins.withBleed.top) {
      addIssue(
        'margin-top-bleed',
        `Your top margin is set to ${settings.marginTop}", but KDP requires at least ${KDP_REQUIREMENTS.minMargins.withBleed.top}" for books with bleed.`,
        'error',
        null,
        true,
        'Top margins that are too small may cause text to be cut off during printing. For books with bleed, KDP requires larger margins.'
      );
    }

    // Check bleed extension
    addIssue(
      'bleed-reminder',
      `You've enabled bleed, which means any background colors or images that should extend to the page edge must extend 0.125" beyond the final trim size.`,
      'info',
      null,
      false,
      'Bleed is an extra 0.125" of content that extends beyond the final page size. This extra area will be trimmed off during printing, but ensures that there are no white edges if your design extends to the page edge.'
    );
    
    if (settings.marginBottom < KDP_REQUIREMENTS.minMargins.withBleed.bottom) {
      addIssue(
        'margin-bottom-bleed',
        `Your bottom margin is set to ${settings.marginBottom}", but KDP requires at least ${KDP_REQUIREMENTS.minMargins.withBleed.bottom}" for books with bleed.`,
        'error',
        null,
        true,
        'Bottom margins that are too small may cause text to be cut off during printing. For books with bleed, KDP requires larger margins.'
      );
    }


  } else {
    // Check margins without bleed (0.25" minimum for top/bottom/outside)
    if (settings.marginInside < KDP_REQUIREMENTS.minMargins.withoutBleed.inside) {
      addIssue(
        'margin-inside',
        `Your inside margin is set to ${settings.marginInside}", but KDP requires at least ${KDP_REQUIREMENTS.minMargins.withoutBleed.inside}".`,
        'error',
        null,
        true,
        'The inside margin (gutter) is the margin closest to the book binding. Adequate margins prevent text from disappearing into the binding.'
      );
    }
    
    if (settings.marginOutside < KDP_REQUIREMENTS.minMargins.withoutBleed.outside) {
      addIssue(
        'margin-outside',
        `Your outside margin is set to ${settings.marginOutside}", but KDP requires at least ${KDP_REQUIREMENTS.minMargins.withoutBleed.outside}".`,
        'error',
        null,
        true,
        'Outside margins that are too small may cause text to be cut off during printing.'
      );
    }
    
    if (settings.marginTop < KDP_REQUIREMENTS.minMargins.withoutBleed.top) {
      addIssue(
        'margin-top',
        `Your top margin is set to ${settings.marginTop}", but KDP requires at least ${KDP_REQUIREMENTS.minMargins.withoutBleed.top}".`,
        'error',
        null,
        true,
        'Top margins that are too small may cause text to be cut off during printing.'
      );
    }

    if (settings.marginBottom < KDP_REQUIREMENTS.minMargins.withoutBleed.bottom) {
      addIssue(
        'margin-bottom',
        `Your bottom margin is set to ${settings.marginBottom}", but KDP requires at least ${KDP_REQUIREMENTS.minMargins.withoutBleed.bottom}".`,
        'error',
        null,
        true,
        'Bottom margins that are too small may cause text to be cut off during printing.'
      );
    }
  }
  
  // Gutter size validation based on page count
  let requiredGutterSize = 0.375; // Default for 24-150 pages
  for (const sizeRule of KDP_REQUIREMENTS.gutterSizeByPageCount) {
    if (pageCount <= sizeRule.maxPages) {
      requiredGutterSize = sizeRule.minGutterSize;
      break;
    }
  }
  
  // Spine text validation
  if (pageCount < KDP_REQUIREMENTS.minSpineTextPages && settings.includeSpineText) {
    addIssue(
      'spine-text-page-count',
      `Your book has ${pageCount} pages, but KDP only allows text on the spine for books with at least ${KDP_REQUIREMENTS.minSpineTextPages} pages.`,
      'error',
      null,
      true,
      'Books with fewer than 79 pages have spines too narrow for text. KDP will reject covers with spine text on thin books.'
    );
  }
  
  // Paper type validation
  if (settings.pageColor === 'color' && settings.paperType === 'cream') {
    addIssue(
      'paper-color-mismatch',
      `You've selected cream paper with color interior. KDP only offers cream paper for black & white interiors.`,
      'error',
      null,
      true,
      'Color interiors can only be printed on white paper. If you want cream paper, you must select black & white interior.'
    );
  }
  
  // Spine width calculation
  let spineWidth = 0;
  if (settings.paperType === 'white' && settings.pageColor === 'white') {
    spineWidth = pageCount * KDP_REQUIREMENTS.spineWidthCalculation.whiteBlackAndWhite;
  } else if (settings.paperType === 'cream' && settings.pageColor === 'white') {
    spineWidth = pageCount * KDP_REQUIREMENTS.spineWidthCalculation.creamBlackAndWhite;
  } else if (settings.paperType === 'white' && settings.pageColor === 'color') {
    spineWidth = pageCount * KDP_REQUIREMENTS.spineWidthCalculation.whiteStandardColor;
  }
  
  // Add info about spine width
  addIssue(
    'spine-width-info',
    `Based on your ${pageCount} pages and ${settings.paperType} ${settings.pageColor === 'color' ? 'color' : 'black & white'} interior, your spine width will be approximately ${spineWidth.toFixed(3)} inches.`,
    'info',
    null,
    false,
    'This is important information if you are designing your own cover. The spine width must be exact to ensure proper printing.'
  );
  
  // File format validation
  if (settings.hasBleed && document.fileFormat !== 'PDF') {
    addIssue(
      'bleed-file-format',
      `You've enabled bleed, but your document is in ${document.fileFormat} format. KDP requires PDF format for books with bleed.`,
      'error',
      null,
      false,
      'When your content extends to the edge of the page, you must submit a PDF file to ensure proper bleed handling.'
    );
  }
  
  if (settings.marginInside < requiredGutterSize) {
    addIssue(
      'gutter-size',
      `Based on your ${pageCount}-page book, the inside margin (gutter) should be at least ${requiredGutterSize}". Currently it's set to ${settings.marginInside}".`,
      'error',
      null,
      true,
      'Books with more pages need larger gutters to ensure text doesn\'t disappear into the binding. KDP specifies gutter sizes based on page count ranges.'
    );
  }
  
  // Font validation
  if (settings.fontSize < KDP_REQUIREMENTS.minFontSize) {
    addIssue(
      'font-size-min',
      `Your font size is set to ${settings.fontSize}pt, which is below KDP's minimum requirement of ${KDP_REQUIREMENTS.minFontSize}pt.`,
      'error',
      null,
      true,
      'Text that is too small may be difficult to read and could cause your submission to be rejected.'
    );
  } else if (settings.fontSize < 10) {
    addIssue(
      'font-size-small',
      `Your font size is set to ${settings.fontSize}pt. While KDP allows fonts as small as 7pt, a font size of at least 10pt is recommended for better readability.`,
      'warning',
      null,
      true,
      'Small fonts can make your book difficult to read, especially for readers with vision impairments.'
    );
  }
  

  
  // Add more thorough checks if requested
  if (thorough) {
    // Check for optimal reading experience
    if (settings.lineSpacing < 1.15) {
      addIssue(
        'line-spacing',
        `Your line spacing is set to ${settings.lineSpacing}. A value of at least 1.15 is recommended for better readability.`,
        'info',
        null,
        true,
        'Adequate line spacing improves readability by preventing text from appearing cramped.'
      );
    }
    
    // Check for balanced margins
    if (Math.abs(settings.marginTop - settings.marginBottom) > 0.25) {
      addIssue(
        'margin-balance',
        `Your top margin (${settings.marginTop}") and bottom margin (${settings.marginBottom}") are significantly different, which may create an unbalanced appearance.`,
        'info',
        null,
        true,
        'Balanced margins create a more professional appearance. Consider making your top and bottom margins more similar.'
      );
    }
    
    // Check for page number position if page numbering is enabled
    if (settings.pageNumbering) {
      addIssue(
        'page-numbers-position',
        `Page numbers are currently set to appear at the ${settings.pageNumberPosition.replace('-', ' ')}. This is fine for most books, but ensure they don\'t appear on chapter title pages.`,
        'info',
        null,
        false,
        'Professional books typically do not show page numbers on chapter title pages, blank pages, or front matter.'
      );
    }
    
    // Font embedding reminder
    addIssue(
      'font-embedding',
      `KDP requires all fonts to be embedded in your final PDF. Our system will handle this automatically when generating your file.`,
      'info',
      null,
      false,
      'Font embedding ensures that your text appears exactly as intended, regardless of what fonts are installed on KDP\'s systems.'
    );
    
    // Image resolution check
    if (document.hasImages) {
      addIssue(
        'image-resolution',
        `Your document contains images. Ensure all images are at least 300 DPI at their final printed size for optimal quality.`,
        'warning',
        null,
        false,
        'Low-resolution images will appear pixelated or blurry in print. For best results, use high-resolution images (300-600 DPI) and insert them at 100% scale.'
      );
    }
    
    // File size check
    const estimatedFileSize = document.fileSize * 1.2; // Rough estimate of final PDF size
    if (estimatedFileSize > KDP_REQUIREMENTS.maxFileSize) {
      addIssue(
        'file-size',
        `Your document may generate a PDF that exceeds KDP's 650MB file size limit. Consider optimizing any images to reduce file size.`,
        'warning',
        null,
        false,
        'Large files can cause upload failures and slow processing. Optimize images to reduce file size while maintaining quality.'
      );
    }
  }
  
  return issues;
}

module.exports = router;