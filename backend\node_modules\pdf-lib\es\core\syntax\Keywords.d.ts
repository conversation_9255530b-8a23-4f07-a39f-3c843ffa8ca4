import CharCodes from "./CharCodes";
export declare const Keywords: {
    header: CharCodes[];
    eof: CharCodes[];
    obj: CharCodes[];
    endobj: CharCodes[];
    xref: CharCodes[];
    trailer: CharCodes[];
    startxref: CharCodes[];
    true: CharCodes[];
    false: CharCodes[];
    null: CharCodes[];
    stream: CharCodes[];
    streamEOF1: CharCodes[];
    streamEOF2: CharCodes[];
    streamEOF3: CharCodes[];
    streamEOF4: CharCodes[];
    endstream: CharCodes[];
    EOF1endstream: CharCodes[];
    EOF2endstream: CharCodes[];
    EOF3endstream: CharCodes[];
};
//# sourceMappingURL=Keywords.d.ts.map