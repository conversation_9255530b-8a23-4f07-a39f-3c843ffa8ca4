{"version": 3, "file": "CustomFontEmbedder.d.ts", "sourceRoot": "", "sources": ["../../../src/core/embedders/CustomFontEmbedder.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,YAAY,EAAE,4BAA0B;AAIvE,OAAO,YAAY,gCAAsC;AACzD,OAAO,MAAM,0BAAgC;AAE7C,OAAO,UAAU,sBAA4B;AAC7C,OAAO,EAEL,KAAK,EAGN,oBAAkB;AAEnB;;;;GAIG;AACH,cAAM,kBAAkB;WACT,GAAG,CACd,OAAO,EAAE,OAAO,EAChB,QAAQ,EAAE,UAAU,EACpB,UAAU,CAAC,EAAE,MAAM,EACnB,YAAY,CAAC,EAAE,YAAY;IAM7B,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC;IACpB,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC;IACvB,QAAQ,CAAC,QAAQ,EAAE,UAAU,CAAC;IAC9B,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC;IAC1B,QAAQ,CAAC,UAAU,EAAE,MAAM,GAAG,SAAS,CAAC;IACxC,QAAQ,CAAC,YAAY,EAAE,YAAY,GAAG,SAAS,CAAC;IAEhD,SAAS,CAAC,YAAY,EAAE,MAAM,CAAC;IAC/B,SAAS,CAAC,UAAU,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;IAErC,SAAS,aACP,IAAI,EAAE,IAAI,EACV,QAAQ,EAAE,UAAU,EACpB,UAAU,CAAC,EAAE,MAAM,EACnB,YAAY,CAAC,EAAE,YAAY;IAa7B;;;OAGG;IACH,UAAU,CAAC,IAAI,EAAE,MAAM,GAAG,YAAY;IAWtC,iBAAiB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,MAAM;IAUrD,kBAAkB,CAChB,IAAI,EAAE,MAAM,EACZ,OAAO,GAAE;QAAE,SAAS,CAAC,EAAE,OAAO,CAAA;KAAO,GACpC,MAAM;IAaT,kBAAkB,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM;IAO1C,gBAAgB,CAAC,OAAO,EAAE,UAAU,EAAE,GAAG,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;cAMpD,aAAa,CAC3B,OAAO,EAAE,UAAU,EACnB,GAAG,CAAC,EAAE,MAAM,GACX,OAAO,CAAC,MAAM,CAAC;IAqBlB,SAAS,CAAC,KAAK,IAAI,OAAO;cAIV,gBAAgB,CAAC,OAAO,EAAE,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC;cAoBtD,mBAAmB,CAAC,OAAO,EAAE,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC;cA4BzD,aAAa,IAAI,OAAO,CAAC,UAAU,CAAC;cAIpC,eAAe,CAAC,OAAO,EAAE,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC;IAOrE,SAAS,CAAC,gBAAgB,CAAC,OAAO,EAAE,UAAU,GAAG,MAAM;IAMvD,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,KAAK,GAAG,MAAM;IAIxC,SAAS,CAAC,aAAa,IAAI,CAAC,MAAM,GAAG,MAAM,EAAE,CAAC,EAAE;IA6BhD,OAAO,CAAC,yBAAyB,CAO/B;CACH;AAED,eAAe,kBAAkB,CAAC"}