{"version": 3, "file": "PDFStreamWriter.js", "sourceRoot": "", "sources": ["../../../src/core/writers/PDFStreamWriter.ts"], "names": [], "mappings": ";AAAA,OAAO,SAAS,8BAAoC;AACpD,OAAO,UAAU,+BAAqC;AACtD,OAAO,gBAAgB,oCAA0C;AACjE,OAAO,OAAO,2BAAiC;AAC/C,OAAO,SAAS,6BAAmC;AAEnD,OAAO,MAAM,0BAAgC;AAC7C,OAAO,SAAS,6BAAmC;AAEnD,OAAO,iBAAiB,wCAA8C;AACtE,OAAO,eAAe,sCAA4C;AAClE,OAAO,SAAS,oBAAmC;AACnD,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,oBAAkB;AAE9C;IAA8B,mCAAS;IAiBrC,yBACE,OAAmB,EACnB,cAAsB,EACtB,aAAsB,EACtB,gBAAwB;QAJ1B,YAME,kBAAM,OAAO,EAAE,cAAc,CAAC,SAI/B;QAFC,KAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,KAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;;IAC3C,CAAC;IAEe,2CAAiB,GAAjC;;;;;;wBACM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,GAAG,CAAC,CAAC;wBAElD,MAAM,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;wBAEtC,IAAI,GAAG,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;wBAE9B,UAAU,GAAG,iBAAiB,CAAC,MAAM,CACzC,IAAI,CAAC,iBAAiB,EAAE,EACxB,IAAI,CAAC,aAAa,CACnB,CAAC;wBAEI,mBAAmB,GAA0B,EAAE,CAAC;wBAChD,iBAAiB,GAA4B,EAAE,CAAC;wBAChD,gBAAgB,GAAa,EAAE,CAAC;wBAEhC,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,wBAAwB,EAAE,CAAC;wBACvD,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,eAAe,CAAC,MAAM;;;6BAAE,CAAA,GAAG,GAAG,GAAG,CAAA;wBACjD,cAAc,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC;wBACrC,GAAG,GAAY,cAAc,GAA1B,EAAE,MAAM,GAAI,cAAc,GAAlB,CAAmB;wBAE/B,iBAAiB,GACrB,GAAG,KAAK,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO;4BACxC,MAAM,YAAY,SAAS;4BAC3B,MAAM,YAAY,gBAAgB;4BAClC,GAAG,CAAC,gBAAgB,KAAK,CAAC,CAAC;6BAEzB,iBAAiB,EAAjB,wBAAiB;wBACnB,mBAAmB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;wBACzC,UAAU,CAAC,oBAAoB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;wBAC3C,IAAI,IAAI,IAAI,CAAC,yBAAyB,CAAC,cAAc,CAAC,CAAC;6BACnD,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAzB,wBAAyB;wBAAE,qBAAM,WAAW,EAAE,EAAA;;wBAAnB,SAAmB,CAAC;;;;wBAE/C,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC;wBAChC,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC;wBAC7C,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,KAAK,CAAC,EAAE;4BACxD,KAAK,GAAG,EAAE,CAAC;4BACX,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;4BAC9B,eAAe,GAAG,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,CAAC;4BAC5C,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;yBACxC;wBACD,UAAU,CAAC,kBAAkB,CAAC,GAAG,EAAE,eAAe,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;wBAClE,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;;;wBAzB4B,GAAG,EAAE,CAAA;;;wBA6BvD,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,iBAAiB,CAAC,MAAM;;;6BAAE,CAAA,GAAG,GAAG,GAAG,CAAA;wBACnD,KAAK,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;wBAC/B,GAAG,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC;wBAE5B,YAAY,GAAG,eAAe,CAAC,qBAAqB,CACxD,IAAI,CAAC,OAAO,EACZ,KAAK,EACL,IAAI,CAAC,aAAa,CACnB,CAAC;wBAEF,UAAU,CAAC,oBAAoB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;wBAC3C,IAAI,IAAI,IAAI,CAAC,yBAAyB,CAAC,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC,CAAC;wBAE5D,mBAAmB,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC,CAAC;6BAE1C,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,MAAM,CAAC,EAApC,wBAAoC;wBAAE,qBAAM,WAAW,EAAE,EAAA;;wBAAnB,SAAmB,CAAC;;;wBAfH,GAAG,EAAE,CAAA;;;wBAkB5D,aAAa,GAAG,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,CAAC;wBAChD,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC;wBACpE,UAAU,CAAC,oBAAoB,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;wBAC/C,UAAU,GAAG,IAAI,CAAC;wBACxB,IAAI,IAAI,IAAI,CAAC,yBAAyB,CAAC,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC,CAAC;wBAEpE,mBAAmB,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC,CAAC;wBAEhD,OAAO,GAAG,UAAU,CAAC,4BAA4B,CAAC,UAAU,CAAC,CAAC;wBACpE,IAAI,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;wBAE9B,sBAAO,EAAE,IAAI,MAAA,EAAE,MAAM,QAAA,EAAE,eAAe,EAAE,mBAAmB,EAAE,OAAO,SAAA,EAAE,EAAC;;;;KACxE;IAxGM,0BAAU,GAAG,UAClB,OAAmB,EACnB,cAAsB,EACtB,aAAoB,EACpB,gBAAqB;QADrB,8BAAA,EAAA,oBAAoB;QACpB,iCAAA,EAAA,qBAAqB;QAErB,OAAA,IAAI,eAAe,CACjB,OAAO,EACP,cAAc,EACd,aAAa,EACb,gBAAgB,CACjB;IALD,CAKC,CAAC;IA8FN,sBAAC;CAAA,AA1GD,CAA8B,SAAS,GA0GtC;AAED,eAAe,eAAe,CAAC"}