{"version": 3, "file": "PDFXRefStreamParser.js", "sourceRoot": "", "sources": ["../../../src/core/parser/PDFXRefStreamParser.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,kBAAwB;AAC/C,OAAO,QAAQ,4BAAkC;AAEjD,OAAO,OAAO,2BAAiC;AAC/C,OAAO,SAAS,6BAAmC;AAEnD,OAAO,MAAM,0BAAgC;AAC7C,OAAO,UAAU,qBAAmC;AAUpD;IAeE,6BAAY,SAAuB;QACjC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAE3B,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC;QAC3B,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QACpD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;QAEjC,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,SAAS,CAAC,CAAC;QAE7D,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;QACpD,IAAI,KAAK,YAAY,QAAQ,EAAE;YAC7B,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;YACtB,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE;gBACzD,IAAM,iBAAiB,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC;gBACtE,IAAM,QAAM,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC;gBAC3D,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,iBAAiB,mBAAA,EAAE,MAAM,UAAA,EAAE,CAAC,CAAC;aACtD;SACF;aAAM;YACL,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE,iBAAiB,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;SACxE;QAED,IAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC;QACtD,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/B,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YAClD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC;SAC5D;IACH,CAAC;IAED,8CAAgB,GAAhB;QACE,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,MAAM,IAAI,YAAY,CAAC,qBAAqB,EAAE,kBAAkB,CAAC,CAAC;SACnE;QACD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAE1B,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG;YACzB,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;YACvC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;YAC7C,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;YACvC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;SACpC,CAAC;QAEF,IAAM,OAAO,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpC,8DAA8D;QAC9D,8BAA8B;QAC9B,qDAAqD;QACrD,IAAI;QAEJ,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,0CAAY,GAApB;QACE,IAAM,OAAO,GAAG,EAAE,CAAC;QACb,IAAA,KAAoD,IAAI,CAAC,UAAU,EAAlE,cAAc,QAAA,EAAE,gBAAgB,QAAA,EAAE,aAAa,QAAmB,CAAC;QAE1E,KACE,IAAI,aAAa,GAAG,CAAC,EAAE,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAC9D,aAAa,GAAG,aAAa,EAC7B,aAAa,EAAE,EACf;YACM,IAAA,KAAgC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,EAA7D,iBAAiB,uBAAA,EAAE,QAAM,YAAoC,CAAC;YAEtE,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,QAAM,EAAE,MAAM,EAAE,EAAE;gBAC9C,IAAI,IAAI,GAAG,CAAC,CAAC;gBACb,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,cAAc,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;oBACxD,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;iBACxC;gBAED,IAAI,MAAM,GAAG,CAAC,CAAC;gBACf,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,gBAAgB,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;oBAC1D,MAAM,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;iBAC5C;gBAED,IAAI,gBAAgB,GAAG,CAAC,CAAC;gBACzB,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,aAAa,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;oBACvD,gBAAgB,GAAG,CAAC,gBAAgB,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;iBAChE;gBAED,oDAAoD;gBACpD,IAAI,cAAc,KAAK,CAAC;oBAAE,IAAI,GAAG,CAAC,CAAC;gBAEnC,IAAM,YAAY,GAAG,iBAAiB,GAAG,MAAM,CAAC;gBAChD,IAAM,KAAK,GAAG;oBACZ,GAAG,EAAE,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC;oBAC9C,MAAM,QAAA;oBACN,OAAO,EAAE,IAAI,KAAK,CAAC;oBACnB,cAAc,EAAE,IAAI,KAAK,CAAC;iBAC3B,CAAC;gBAEF,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACrB;SACF;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IA5GM,6BAAS,GAAG,UAAC,SAAuB;QACzC,OAAA,IAAI,mBAAmB,CAAC,SAAS,CAAC;IAAlC,CAAkC,CAAC;IA4GvC,0BAAC;CAAA,AA9GD,IA8GC;AAED,eAAe,mBAAmB,CAAC"}