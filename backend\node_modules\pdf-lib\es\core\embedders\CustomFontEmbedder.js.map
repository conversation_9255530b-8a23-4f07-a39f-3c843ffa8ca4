{"version": 3, "file": "CustomFontEmbedder.js", "sourceRoot": "", "sources": ["../../../src/core/embedders/CustomFontEmbedder.ts"], "names": [], "mappings": ";AAEA,OAAO,EAAE,UAAU,EAAE,eAAgC;AACrD,OAAO,EAAE,eAAe,EAAE,oBAAqC;AAC/D,OAAO,YAAY,gCAAsC;AAEzD,OAAO,SAAS,6BAAmC;AAEnD,OAAO,EACL,aAAa,EACb,KAAK,EACL,UAAU,EACV,sBAAsB,GACvB,oBAAkB;AAEnB;;;;GAIG;AACH;IAqBE,4BACE,IAAU,EACV,QAAoB,EACpB,UAAmB,EACnB,YAA2B;QAJ7B,iBAeC;QAsLO,8BAAyB,GAAG;YAClC,IAAM,MAAM,GAAY,IAAI,KAAK,CAAC,KAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YACjE,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;gBACvD,IAAM,SAAS,GAAG,KAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;gBAC9C,MAAM,CAAC,GAAG,CAAC,GAAG,KAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;aACtD;YACD,OAAO,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,EAAE,EAAJ,CAAI,CAAC,CAAC;QAC7D,CAAC,CAAC;QAtMA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;QACzC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,MAAM,CAAC;QACnD,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QAEjC,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;IACtE,CAAC;IAnCY,sBAAG,GAAhB,UACE,OAAgB,EAChB,QAAoB,EACpB,UAAmB,EACnB,YAA2B;;;;;4BAEd,qBAAM,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAA;;wBAArC,IAAI,GAAG,SAA8B;wBAC3C,sBAAO,IAAI,kBAAkB,CAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,CAAC,EAAC;;;;KACzE;IA6BD;;;OAGG;IACH,uCAAU,GAAV,UAAW,IAAY;QACb,IAAA,MAAM,GAAK,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,OAA9C,CAA+C;QAC7D,IAAM,QAAQ,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAC1C,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YACvD,QAAQ,CAAC,GAAG,CAAC,GAAG,sBAAsB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;SAC3D;QACD,OAAO,YAAY,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5C,CAAC;IAED,yEAAyE;IACzE,8DAA8D;IAC9D,8CAAiB,GAAjB,UAAkB,IAAY,EAAE,IAAY;QAClC,IAAA,MAAM,GAAK,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,OAA9C,CAA+C;QAC7D,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YACvD,UAAU,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC;SACrD;QACD,IAAM,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC;QAC1B,OAAO,UAAU,GAAG,KAAK,CAAC;IAC5B,CAAC;IAED,+CAAkB,GAAlB,UACE,IAAY,EACZ,OAAqC;QAArC,wBAAA,EAAA,YAAqC;QAE7B,IAAA,KAAqB,OAAO,UAAZ,EAAhB,SAAS,mBAAG,IAAI,KAAA,CAAa;QAE/B,IAAA,KAA4B,IAAI,CAAC,IAAI,EAAnC,MAAM,YAAA,EAAE,OAAO,aAAA,EAAE,IAAI,UAAc,CAAC;QAC5C,IAAM,IAAI,GAAG,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;QAChD,IAAM,OAAO,GAAG,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;QAEpD,IAAI,MAAM,GAAG,IAAI,GAAG,OAAO,CAAC;QAC5B,IAAI,CAAC,SAAS;YAAE,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAEjD,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;IAChC,CAAC;IAED,+CAAkB,GAAlB,UAAmB,MAAc;QACzB,IAAA,KAA4B,IAAI,CAAC,IAAI,EAAnC,MAAM,YAAA,EAAE,OAAO,aAAA,EAAE,IAAI,UAAc,CAAC;QAC5C,IAAM,IAAI,GAAG,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;QAChD,IAAM,OAAO,GAAG,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;QACpD,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC;IAC5C,CAAC;IAED,6CAAgB,GAAhB,UAAiB,OAAmB,EAAE,GAAY;QAChD,IAAI,CAAC,YAAY;YACf,IAAI,CAAC,UAAU,IAAI,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5D,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IAC1C,CAAC;IAEe,0CAAa,GAA7B,UACE,OAAmB,EACnB,GAAY;;;;;4BAEW,qBAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAA;;wBAArD,cAAc,GAAG,SAAoC;wBACrD,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;wBAEhD,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC;4BAC3B,IAAI,EAAE,MAAM;4BACZ,OAAO,EAAE,OAAO;4BAChB,QAAQ,EAAE,IAAI,CAAC,YAAY;4BAC3B,QAAQ,EAAE,YAAY;4BACtB,eAAe,EAAE,CAAC,cAAc,CAAC;4BACjC,SAAS,EAAE,cAAc;yBAC1B,CAAC,CAAC;wBAEH,IAAI,GAAG,EAAE;4BACP,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;4BAC9B,sBAAO,GAAG,EAAC;yBACZ;6BAAM;4BACL,sBAAO,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAC;yBACnC;;;;;KACF;IAES,kCAAK,GAAf;QACE,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;IACvB,CAAC;IAEe,6CAAgB,GAAhC,UAAiC,OAAmB;;;;;4BACxB,qBAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAA;;wBAA3D,iBAAiB,GAAG,SAAuC;wBAE3D,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC;4BAC9B,IAAI,EAAE,MAAM;4BACZ,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc;4BACvD,WAAW,EAAE,UAAU;4BACvB,QAAQ,EAAE,IAAI,CAAC,YAAY;4BAC3B,aAAa,EAAE;gCACb,QAAQ,EAAE,SAAS,CAAC,EAAE,CAAC,OAAO,CAAC;gCAC/B,QAAQ,EAAE,SAAS,CAAC,EAAE,CAAC,UAAU,CAAC;gCAClC,UAAU,EAAE,CAAC;6BACd;4BACD,cAAc,EAAE,iBAAiB;4BACjC,CAAC,EAAE,IAAI,CAAC,aAAa,EAAE;yBACxB,CAAC,CAAC;wBAEH,sBAAO,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAC;;;;KACtC;IAEe,gDAAmB,GAAnC,UAAoC,OAAmB;;;;;;4BAC/B,qBAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,EAAA;;wBAAnD,aAAa,GAAG,SAAmC;wBAEjD,KAAK,GAAK,IAAI,MAAT,CAAU;wBACjB,KAAuD,IAAI,CAAC,IAAI,EAA9D,WAAW,iBAAA,EAAE,MAAM,YAAA,EAAE,OAAO,aAAA,EAAE,SAAS,eAAA,EAAE,OAAO,aAAA,CAAe;wBACjE,KAA6B,IAAI,CAAC,IAAI,CAAC,IAAI,EAAzC,IAAI,UAAA,EAAE,IAAI,UAAA,EAAE,IAAI,UAAA,EAAE,IAAI,UAAA,CAAoB;wBAE5C,cAAc,GAAG,OAAO,CAAC,GAAG;gCAChC,IAAI,EAAE,gBAAgB;gCACtB,QAAQ,EAAE,IAAI,CAAC,YAAY;gCAC3B,KAAK,EAAE,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;gCACjC,QAAQ,EAAE,CAAC,IAAI,GAAG,KAAK,EAAE,IAAI,GAAG,KAAK,EAAE,IAAI,GAAG,KAAK,EAAE,IAAI,GAAG,KAAK,CAAC;gCAClE,WAAW,EAAE,WAAW;gCACxB,MAAM,EAAE,MAAM,GAAG,KAAK;gCACtB,OAAO,EAAE,OAAO,GAAG,KAAK;gCACxB,SAAS,EAAE,CAAC,SAAS,IAAI,MAAM,CAAC,GAAG,KAAK;gCACxC,OAAO,EAAE,CAAC,OAAO,IAAI,CAAC,CAAC,GAAG,KAAK;gCAE/B,iEAAiE;gCACjE,gFAAgF;gCAChF,KAAK,EAAE,CAAC;;4BAER,GAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,IAAG,aAAa;gCACzD,CAAC;wBAEH,sBAAO,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAC;;;;KACzC;IAEe,0CAAa,GAA7B;;;gBACE,sBAAO,IAAI,CAAC,QAAQ,EAAC;;;KACtB;IAEe,4CAAe,GAA/B,UAAgC,OAAmB;;;;;;wBAC9B,KAAA,CAAA,KAAA,OAAO,CAAA,CAAC,WAAW,CAAA;wBAAC,qBAAM,IAAI,CAAC,aAAa,EAAE,EAAA;;wBAA3D,UAAU,GAAG,cAAoB,SAA0B,EAAE;gCACjE,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS;6BACpD,EAAC;wBACF,sBAAO,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAC;;;;KACrC;IAES,6CAAgB,GAA1B,UAA2B,OAAmB;QAC5C,IAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3E,IAAM,UAAU,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAC7C,OAAO,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IACtC,CAAC;IAES,oCAAO,GAAjB,UAAkB,KAAa;QAC7B,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC;IAES,0CAAa,GAAvB;QACE,IAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;QAExC,IAAM,MAAM,GAA0B,EAAE,CAAC;QACzC,IAAI,WAAW,GAAa,EAAE,CAAC;QAE/B,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YACvD,IAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;YAC9B,IAAM,SAAS,GAAG,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;YAElC,IAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAC5C,IAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAE5C,IAAI,GAAG,KAAK,CAAC,EAAE;gBACb,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;aAC1B;iBAAM,IAAI,WAAW,GAAG,WAAW,KAAK,CAAC,EAAE;gBAC1C,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACzB,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACzB,WAAW,GAAG,EAAE,CAAC;aAClB;YAED,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;SACvD;QAED,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAEzB,OAAO,MAAM,CAAC;IAChB,CAAC;IAUH,yBAAC;AAAD,CAAC,AAlOD,IAkOC;AAED,eAAe,kBAAkB,CAAC"}