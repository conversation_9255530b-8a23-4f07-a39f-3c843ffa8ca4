{"version": 3, "file": "PngEmbedder.js", "sourceRoot": "", "sources": ["../../../src/core/embedders/PngEmbedder.ts"], "names": [], "mappings": ";AAEA,OAAO,EAAE,GAAG,EAAE,wBAAsB;AAEpC;;;;GAIG;AACH;IAaE,qBAAoB,GAAQ;QAC1B,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;QACjB,IAAI,CAAC,gBAAgB,GAAG,GAAG,CAAC,gBAAgB,CAAC;QAC7C,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,WAAW,CAAC;IAChC,CAAC;IAlBY,eAAG,GAAhB,UAAiB,SAAqB;;;;gBAC9B,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAChC,sBAAO,IAAI,WAAW,CAAC,GAAG,CAAC,EAAC;;;KAC7B;IAiBK,sCAAgB,GAAtB,UAAuB,OAAmB,EAAE,GAAY;;;;gBAChD,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;gBAExC,OAAO,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE;oBACzD,IAAI,EAAE,SAAS;oBACf,OAAO,EAAE,OAAO;oBAChB,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB;oBAC7C,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK;oBACvB,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;oBACzB,UAAU,EAAE,IAAI,CAAC,UAAU;oBAC3B,KAAK,OAAA;iBACN,CAAC,CAAC;gBAEH,IAAI,GAAG,EAAE;oBACP,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;oBAC7B,sBAAO,GAAG,EAAC;iBACZ;qBAAM;oBACL,sBAAO,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAC;iBAClC;;;;KACF;IAEO,uCAAiB,GAAzB,UAA0B,OAAmB;QAC3C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY;YAAE,OAAO,SAAS,CAAC;QAE/C,IAAM,OAAO,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE;YAC3D,IAAI,EAAE,SAAS;YACf,OAAO,EAAE,OAAO;YAChB,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;YACzB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK;YACvB,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB;YAC7C,UAAU,EAAE,YAAY;YACxB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;SACf,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IACnC,CAAC;IACH,kBAAC;AAAD,CAAC,AAzDD,IAyDC;AAED,eAAe,WAAW,CAAC"}