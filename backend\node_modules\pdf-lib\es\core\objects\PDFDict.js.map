{"version": 3, "file": "PDFDict.js", "sourceRoot": "", "sources": ["../../../src/core/objects/PDFDict.ts"], "names": [], "mappings": ";AAGA,OAAO,OAAO,kBAAiC;AAC/C,OAAO,OAAO,kBAAiC;AAE/C,OAAO,SAAS,oBAAmC;AAKnD,OAAO,SAAS,4BAAkC;AAIlD;IAAsB,2BAAS;IAU7B,iBAAsB,GAAY,EAAE,OAAmB;QAAvD,YACE,iBAAO,SAGR;QAFC,KAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAChB,KAAI,CAAC,OAAO,GAAG,OAAO,CAAC;;IACzB,CAAC;IAED,sBAAI,GAAJ;QACE,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;IACtC,CAAC;IAED,wBAAM,GAAN;QACE,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;IACxC,CAAC;IAED,yBAAO,GAAP;QACE,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;IACzC,CAAC;IAED,qBAAG,GAAH,UAAI,GAAY,EAAE,KAAgB;QAChC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAC5B,CAAC;IAED,qBAAG,GAAH,UACE,GAAY;IACZ,oEAAoE;IACpE,uCAAuC;IACvC,eAAuB;QAAvB,gCAAA,EAAA,uBAAuB;QAEvB,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACjC,IAAI,KAAK,KAAK,OAAO,IAAI,CAAC,eAAe;YAAE,OAAO,SAAS,CAAC;QAC5D,OAAO,KAAK,CAAC;IACf,CAAC;IAED,qBAAG,GAAH,UAAI,GAAY;QACd,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACjC,OAAO,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,OAAO,CAAC;IAClD,CAAC;IAgCD,6BAAW,GAAX,UAAY,GAAY;;QAAE,eAAe;aAAf,UAAe,EAAf,qBAAe,EAAf,IAAe;YAAf,8BAAe;;QACvC,oEAAoE;QACpE,uCAAuC;QACvC,IAAM,eAAe,GAAG,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAEhD,IAAM,KAAK,GAAG,CAAA,KAAA,IAAI,CAAC,OAAO,CAAA,CAAC,WAAW,2BACpC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,eAAe,CAAC,GAE3B,KAAK,EACF,CAAC;QAET,IAAI,KAAK,KAAK,OAAO,IAAI,CAAC,eAAe;YAAE,OAAO,SAAS,CAAC;QAE5D,OAAO,KAAK,CAAC;IACf,CAAC;IA8BD,wBAAM,GAAN,UAAO,GAAY;;QAAE,eAAe;aAAf,UAAe,EAAf,qBAAe,EAAf,IAAe;YAAf,8BAAe;;QAClC,oEAAoE;QACpE,uCAAuC;QACvC,IAAM,eAAe,GAAG,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAEhD,IAAM,KAAK,GAAG,CAAA,KAAA,IAAI,CAAC,OAAO,CAAA,CAAC,MAAM,2BAC/B,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,eAAe,CAAC,GAE3B,KAAK,EACF,CAAC;QAET,IAAI,KAAK,KAAK,OAAO,IAAI,CAAC,eAAe;YAAE,OAAO,SAAS,CAAC;QAE5D,OAAO,KAAK,CAAC;IACf,CAAC;IAED,wBAAM,GAAN,UAAO,GAAY;QACjB,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAC/B,CAAC;IAED,uBAAK,GAAL;QACE,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED,kEAAkE;IAClE,2BAAS,GAAT,UAAU,GAAQ;QAAR,oBAAA,EAAA,QAAQ;QAChB,IAAM,YAAY,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QACjC,IAAI,GAAG,GAAG,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;QAC5D,OAAO,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YACjC,GAAG,GAAG,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;SACzD;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,uBAAK,GAAL,UAAM,OAAoB;QACxB,IAAM,KAAK,GAAG,OAAO,CAAC,WAAW,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;QAC3D,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC/B,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YAClD,IAAA,KAAe,OAAO,CAAC,GAAG,CAAC,EAA1B,GAAG,QAAA,EAAE,KAAK,QAAgB,CAAC;YAClC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;SACvB;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,0BAAQ,GAAR;QACE,IAAI,UAAU,GAAG,MAAM,CAAC;QACxB,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC/B,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YAClD,IAAA,KAAe,OAAO,CAAC,GAAG,CAAC,EAA1B,GAAG,QAAA,EAAE,KAAK,QAAgB,CAAC;YAClC,UAAU,IAAI,GAAG,CAAC,QAAQ,EAAE,GAAG,GAAG,GAAG,KAAK,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC;SAC9D;QACD,UAAU,IAAI,IAAI,CAAC;QACnB,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,6BAAW,GAAX;QACE,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC/B,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YAClD,IAAA,KAAe,OAAO,CAAC,GAAG,CAAC,EAA1B,GAAG,QAAA,EAAE,KAAK,QAAgB,CAAC;YAClC,IAAI,IAAI,GAAG,CAAC,WAAW,EAAE,GAAG,KAAK,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;SACrD;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,+BAAa,GAAb,UAAc,MAAkB,EAAE,MAAc;QAC9C,IAAM,aAAa,GAAG,MAAM,CAAC;QAE7B,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,CAAC,QAAQ,CAAC;QACtC,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,CAAC,QAAQ,CAAC;QACtC,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC;QAErC,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC/B,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YAClD,IAAA,KAAe,OAAO,CAAC,GAAG,CAAC,EAA1B,GAAG,QAAA,EAAE,KAAK,QAAgB,CAAC;YAClC,MAAM,IAAI,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC;YACnC,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC;SACtC;QAED,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,CAAC,WAAW,CAAC;QACzC,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,CAAC,WAAW,CAAC;QAEzC,OAAO,MAAM,GAAG,aAAa,CAAC;IAChC,CAAC;IA9MM,mBAAW,GAAG,UAAC,OAAmB,IAAK,OAAA,IAAI,OAAO,CAAC,IAAI,GAAG,EAAE,EAAE,OAAO,CAAC,EAA/B,CAA+B,CAAC;IAEvE,0BAAkB,GAAG,UAAC,GAAY,EAAE,OAAmB;QAC5D,OAAA,IAAI,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC;IAAzB,CAAyB,CAAC;IA4M9B,cAAC;CAAA,AAhND,CAAsB,SAAS,GAgN9B;AAED,eAAe,OAAO,CAAC"}