{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\My Project\\\\KDP\\\\frontend\\\\src\\\\pages\\\\FormatSettings.js\";\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport axios from 'axios';\nimport './FormatSettings.css';\nimport Tooltip from '../components/common/Tooltip';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FormatSettings = () => {\n  var _location$state, _trimSizeOptions$find, _paperTypeOptions$fin;\n  const navigate = useNavigate();\n  const location = useLocation();\n  const documentId = (_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.documentId;\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [documentInfo, setDocumentInfo] = useState(null);\n\n  // Format settings\n  const [settings, setSettings] = useState({\n    trimSize: '6x9',\n    pageColor: 'white',\n    marginTop: 1,\n    marginBottom: 1,\n    marginInside: 0.75,\n    marginOutside: 0.75,\n    hasBleed: false,\n    paperType: 'white',\n    pageNumbering: true,\n    pageNumberPosition: 'bottom-center',\n    fontName: 'Times New Roman',\n    fontSize: 12,\n    lineSpacing: 1.5,\n    includeTOC: true,\n    includeChapterBreaks: true\n  });\n\n  // Available options based on KDP guidelines based on KDP guidelines\n  const trimSizeOptions = [{\n    value: '5x8',\n    label: '5\" x 8\" (12.7 x 20.32 cm)',\n    description: 'Common for fiction, novellas, short non-fiction'\n  }, {\n    value: '5.5x8.5',\n    label: '5.5\" x 8.5\" (13.97 x 21.59 cm)',\n    description: 'Popular size for fiction and non-fiction'\n  }, {\n    value: '6x9',\n    label: '6\" x 9\" (15.24 x 22.86 cm)',\n    description: 'Standard for textbooks and larger novels (KDP recommended)'\n  }, {\n    value: '7x10',\n    label: '7\" x 10\" (17.78 x 25.4 cm)',\n    description: 'Good for workbooks and textbooks'\n  }, {\n    value: '8.5x11',\n    label: '8.5\" x 11\" (21.59 x 27.94 cm)',\n    description: 'Letter size, good for large-format books'\n  }\n  // KDP allows custom sizes within 4\"–8.5\" width and 6\"–11.69\" height\n  // KDP allows custom sizes within 4\"–8.5\" width and 6\"–11.69\" height\n  ];\n  const paperTypeOptions = [{\n    value: 'white',\n    label: 'White Paper',\n    description: 'Bright white paper, good contrast for text and images'\n  }, {\n    value: 'cream',\n    label: 'Cream Paper',\n    description: 'Warmer tone, easier on the eyes, popular for fiction (only available for black & white interior) (only available for black & white interior)'\n  }];\n  const fontOptions = [{\n    value: 'Times New Roman',\n    label: 'Times New Roman',\n    description: 'Classic serif font, very readable'\n  }, {\n    value: 'Garamond',\n    label: 'Garamond',\n    description: 'Elegant serif font with traditional look'\n  }, {\n    value: 'Georgia',\n    label: 'Georgia',\n    description: 'Clear, readable serif designed for screens'\n  }, {\n    value: 'Arial',\n    label: 'Arial',\n    description: 'Clean sans-serif font'\n  }, {\n    value: 'Helvetica',\n    label: 'Helvetica',\n    description: 'Modern sans-serif with neutral appearance'\n  }, {\n    value: 'Baskerville',\n    label: 'Baskerville',\n    description: 'Traditional serif with good readability'\n  }];\n\n  // KDP gutter size requirements based on page count\n  const gutterSizeByPageCount = [{\n    maxPages: 150,\n    minGutterSize: 0.375\n  }, {\n    maxPages: 300,\n    minGutterSize: 0.5\n  }, {\n    maxPages: 500,\n    minGutterSize: 0.625\n  }, {\n    maxPages: 700,\n    minGutterSize: 0.75\n  }, {\n    maxPages: 828,\n    minGutterSize: 0.875\n  }];\n\n  // Get minimum required gutter size based on page count\n  const getMinGutterSize = pageCount => {\n    for (const sizeRule of gutterSizeByPageCount) {\n      if (pageCount <= sizeRule.maxPages) {\n        return sizeRule.minGutterSize;\n      }\n    }\n    return 0.875; // Default to largest size for very large books\n  };\n\n  // Fetch document info\n  useEffect(() => {\n    if (!documentId) {\n      navigate('/upload');\n      return;\n    }\n    const fetchDocumentInfo = async () => {\n      try {\n        setLoading(true);\n        const response = await axios.get(`/api/document/${documentId}`);\n        setDocumentInfo(response.data);\n\n        // Set initial settings based on document info if available\n        if (response.data.recommendedSettings) {\n          setSettings(prevSettings => ({\n            ...prevSettings,\n            ...response.data.recommendedSettings\n          }));\n        }\n      } catch (err) {\n        console.error('Error fetching document info:', err);\n        setError('Failed to load document information. Please try again.');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchDocumentInfo();\n  }, [documentId, navigate]);\n\n  // Handle form field changes\n  const handleChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n\n    // Update settings and handle dependent values\n    setSettings(prevSettings => {\n      const newSettings = {\n        ...prevSettings,\n        [name]: type === 'checkbox' ? checked : value\n      };\n\n      // Special handling for dependent settings\n      if (name === 'hasBleed' && checked) {\n        // When enabling bleed, ensure margins meet minimum requirements\n        newSettings.marginTop = Math.max(newSettings.marginTop, 0.375);\n        newSettings.marginBottom = Math.max(newSettings.marginBottom, 0.375);\n        newSettings.marginOutside = Math.max(newSettings.marginOutside, 0.375);\n        newSettings.marginInside = Math.max(newSettings.marginInside, getMinGutterSize((documentInfo === null || documentInfo === void 0 ? void 0 : documentInfo.pageCount) || 150));\n      }\n\n      // When changing page color, handle paper type compatibility\n      if (name === 'pageColor' && value === 'color') {\n        // Color interior can only use white paper\n        newSettings.paperType = 'white';\n      }\n\n      // When page count is very low, disable spine text\n      if ((name === 'documentId' || name === 'pageCount') && (documentInfo === null || documentInfo === void 0 ? void 0 : documentInfo.pageCount) < 79) {\n        newSettings.includeSpineText = false;\n      }\n      return newSettings;\n    });\n  };\n\n  // Handle numeric input with limits, accounting for KDP requirements\n  const handleNumericChange = (e, min, max) => {\n    const {\n      name,\n      value\n    } = e.target;\n    const numValue = parseFloat(value);\n    if (isNaN(numValue)) {\n      setSettings(prevSettings => ({\n        ...prevSettings,\n        [name]: ''\n      }));\n      return;\n    }\n\n    // Get minimum value based on KDP requirements\n    let actualMin = min;\n    if (name === 'marginInside') {\n      // Inside margin (gutter) has special minimum based on page count\n      actualMin = getMinGutterSize((documentInfo === null || documentInfo === void 0 ? void 0 : documentInfo.pageCount) || 150);\n    } else if (settings.hasBleed && (name === 'marginTop' || name === 'marginBottom' || name === 'marginOutside')) {\n      // For bleed documents, outside/top/bottom margins must be at least 0.375\"\n      actualMin = Math.max(min, 0.375);\n    } else if (!settings.hasBleed && (name === 'marginTop' || name === 'marginBottom' || name === 'marginOutside')) {\n      // For non-bleed documents, outside/top/bottom margins must be at least 0.25\"\n      actualMin = Math.max(min, 0.25);\n    }\n    if (numValue < actualMin) {\n      setSettings(prevSettings => ({\n        ...prevSettings,\n        [name]: actualMin\n      }));\n    } else if (numValue > max) {\n      setSettings(prevSettings => ({\n        ...prevSettings,\n        [name]: max\n      }));\n    } else {\n      setSettings(prevSettings => ({\n        ...prevSettings,\n        [name]: numValue\n      }));\n    }\n  };\n\n  // Submit settings and navigate to preview\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      const response = await axios.post(`/api/document/${documentId}/settings`, settings);\n      navigate('/preview', {\n        state: {\n          documentId,\n          formatId: response.data.formatId\n        }\n      });\n    } catch (err) {\n      var _err$response, _err$response$data;\n      console.error('Error saving settings:', err);\n      setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || 'Failed to save formatting settings. Please try again.');\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"format-settings-page\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Loading document information...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"format-settings-page\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Error\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigate('/upload'),\n            className: \"btn btn-primary\",\n            children: \"Back to Upload\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"format-settings-page\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"format-settings-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Format Your Book\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this), documentInfo && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"document-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Document Information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-label\",\n                children: \"File Name:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-value\",\n                children: documentInfo.fileName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-label\",\n                children: \"Pages:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-value\",\n                children: documentInfo.pageCount\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-label\",\n                children: \"Word Count:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-value\",\n                children: documentInfo.wordCount.toLocaleString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-label\",\n                children: \"File Format:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-value\",\n                children: documentInfo.fileFormat\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Book Size & Margins\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                htmlFor: \"trimSize\",\n                children: [\"Trim Size\", /*#__PURE__*/_jsxDEV(Tooltip, {\n                  content: \"The physical dimensions of your book. The 6\\xD79 inch size is the most cost-effective option for most books.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"trimSize\",\n                name: \"trimSize\",\n                value: settings.trimSize,\n                onChange: handleChange,\n                className: \"form-control\",\n                children: trimSizeOptions.map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: option.value,\n                  children: option.label\n                }, option.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"form-hint\",\n                children: (_trimSizeOptions$find = trimSizeOptions.find(o => o.value === settings.trimSize)) === null || _trimSizeOptions$find === void 0 ? void 0 : _trimSizeOptions$find.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: [\"Margins\", /*#__PURE__*/_jsxDEV(Tooltip, {\n                  content: \"Margins are the white space around your text. Inside margins need to be larger to account for binding.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"margins-grid\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"marginTop\",\n                    children: \"Top (inches)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 303,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    id: \"marginTop\",\n                    name: \"marginTop\",\n                    type: \"number\",\n                    step: \"0.1\",\n                    min: \"0.5\",\n                    max: \"3\",\n                    value: settings.marginTop,\n                    onChange: e => handleNumericChange(e, 0.5, 3),\n                    className: \"form-control\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"marginBottom\",\n                    children: \"Bottom (inches)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    id: \"marginBottom\",\n                    name: \"marginBottom\",\n                    type: \"number\",\n                    step: \"0.1\",\n                    min: \"0.5\",\n                    max: \"3\",\n                    value: settings.marginBottom,\n                    onChange: e => handleNumericChange(e, 0.5, 3),\n                    className: \"form-control\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 318,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"marginInside\",\n                    children: \"Inside (inches)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 331,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    id: \"marginInside\",\n                    name: \"marginInside\",\n                    type: \"number\",\n                    step: \"0.1\",\n                    min: \"0.5\",\n                    max: \"3\",\n                    value: settings.marginInside,\n                    onChange: e => handleNumericChange(e, 0.5, 3),\n                    className: \"form-control\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"marginOutside\",\n                    children: \"Outside (inches)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 345,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    id: \"marginOutside\",\n                    name: \"marginOutside\",\n                    type: \"number\",\n                    step: \"0.1\",\n                    min: \"0.5\",\n                    max: \"3\",\n                    value: settings.marginOutside,\n                    onChange: e => handleNumericChange(e, 0.5, 3),\n                    className: \"form-control\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 346,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"checkbox-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"hasBleed\",\n                  name: \"hasBleed\",\n                  type: \"checkbox\",\n                  checked: settings.hasBleed,\n                  onChange: handleChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"hasBleed\",\n                  children: [\"Include bleed\", /*#__PURE__*/_jsxDEV(Tooltip, {\n                    content: \"Only enable bleed if your book contains images that extend to the edge of the page. This adds 0.125 inches to the outside edges.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 370,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 17\n              }, this), settings.hasBleed && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bleed-warning\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Important:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 378,\n                    columnNumber: 23\n                  }, this), \" KDP requires PDF format for books with bleed.\", document.fileFormat !== 'PDF' && \" Your document will be converted to PDF.\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"small-text\",\n                  children: \"Bleed area (0.125\\\" on top, bottom, and outside edge) will be trimmed during printing. Make sure all important content stays within the safe zone.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Paper & Appearance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                htmlFor: \"paperType\",\n                children: [\"Paper Type\", /*#__PURE__*/_jsxDEV(Tooltip, {\n                  content: \"White paper provides better contrast for text and images. Cream paper is easier on the eyes for extended reading.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"paperType\",\n                name: \"paperType\",\n                value: settings.paperType,\n                onChange: handleChange,\n                className: \"form-control\",\n                children: paperTypeOptions.map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: option.value,\n                  children: option.label\n                }, option.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"form-hint\",\n                children: (_paperTypeOptions$fin = paperTypeOptions.find(o => o.value === settings.paperType)) === null || _paperTypeOptions$fin === void 0 ? void 0 : _paperTypeOptions$fin.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 17\n              }, this), settings.pageColor === 'color' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"color-note\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"small-text\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Note:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 415,\n                    columnNumber: 23\n                  }, this), \" Color interiors are only available with white paper.\", settings.paperType === 'cream' && ' Your paper type has been changed to white.']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 414,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                htmlFor: \"pageColor\",\n                children: [\"Interior Color\", /*#__PURE__*/_jsxDEV(Tooltip, {\n                  content: \"Black & white is more affordable. Color is best for books with photos or colored illustrations.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"pageColor\",\n                name: \"pageColor\",\n                value: settings.pageColor,\n                onChange: handleChange,\n                className: \"form-control\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"white\",\n                  children: \"Black & White\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"color\",\n                  children: \"Full Color (Higher Cost)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 17\n              }, this), settings.pageColor === 'color' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"color-note\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"small-text\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Note:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 440,\n                    columnNumber: 23\n                  }, this), \" Color interiors are only available with white paper.\", settings.paperType === 'cream' && ' Your paper type has been changed to white.']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Text Formatting\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                htmlFor: \"fontName\",\n                children: [\"Font\", /*#__PURE__*/_jsxDEV(Tooltip, {\n                  content: \"Choose a font that's easy to read. Serif fonts (like Times New Roman) are traditional for books.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"fontName\",\n                name: \"fontName\",\n                value: settings.fontName,\n                onChange: handleChange,\n                className: \"form-control\",\n                children: fontOptions.map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: option.value,\n                  children: option.label\n                }, option.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 456,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  htmlFor: \"fontSize\",\n                  children: [\"Font Size\", /*#__PURE__*/_jsxDEV(Tooltip, {\n                    content: \"11-12pt is standard for books. Larger sizes are easier to read but use more pages.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 475,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  id: \"fontSize\",\n                  name: \"fontSize\",\n                  value: settings.fontSize,\n                  onChange: handleChange,\n                  className: \"form-control\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"10\",\n                    children: \"10pt (Small)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 484,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"11\",\n                    children: \"11pt\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 485,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"12\",\n                    children: \"12pt (Standard)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 486,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"14\",\n                    children: \"14pt (Larger)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 487,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"16\",\n                    children: \"16pt (Very Large)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 477,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  htmlFor: \"lineSpacing\",\n                  children: [\"Line Spacing\", /*#__PURE__*/_jsxDEV(Tooltip, {\n                    content: \"The space between lines of text. 1.5 is standard for easy reading.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 495,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 493,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  id: \"lineSpacing\",\n                  name: \"lineSpacing\",\n                  value: settings.lineSpacing,\n                  onChange: handleChange,\n                  className: \"form-control\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"1\",\n                    children: \"Single (1.0)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 504,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"1.15\",\n                    children: \"1.15\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 505,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"1.5\",\n                    children: \"1.5 (Standard)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 506,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"2\",\n                    children: \"Double (2.0)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 507,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 497,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Book Structure\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"checkbox-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"pageNumbering\",\n                  name: \"pageNumbering\",\n                  type: \"checkbox\",\n                  checked: settings.pageNumbering,\n                  onChange: handleChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 518,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"pageNumbering\",\n                  children: [\"Add page numbers\", /*#__PURE__*/_jsxDEV(Tooltip, {\n                    content: \"Page numbers are recommended for all books.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 527,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 525,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 15\n            }, this), settings.pageNumbering && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                htmlFor: \"pageNumberPosition\",\n                children: \"Page Number Position\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"pageNumberPosition\",\n                name: \"pageNumberPosition\",\n                value: settings.pageNumberPosition,\n                onChange: handleChange,\n                className: \"form-control\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"bottom-center\",\n                  children: \"Bottom Center\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 544,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"bottom-outside\",\n                  children: \"Bottom Outside\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 545,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"top-center\",\n                  children: \"Top Center\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 546,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"top-outside\",\n                  children: \"Top Outside\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 547,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"checkbox-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"includeTOC\",\n                  name: \"includeTOC\",\n                  type: \"checkbox\",\n                  checked: settings.includeTOC,\n                  onChange: handleChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 554,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"includeTOC\",\n                  children: [\"Generate Table of Contents\", /*#__PURE__*/_jsxDEV(Tooltip, {\n                    content: \"Automatically create a table of contents based on your chapter headings.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 563,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 561,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 552,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"checkbox-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"includeChapterBreaks\",\n                  name: \"includeChapterBreaks\",\n                  type: \"checkbox\",\n                  checked: settings.includeChapterBreaks,\n                  onChange: handleChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"includeChapterBreaks\",\n                  children: [\"Force chapters to start on right pages\", /*#__PURE__*/_jsxDEV(Tooltip, {\n                    content: \"Professional books typically start each chapter on a right-facing (odd-numbered) page.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 579,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 577,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 513,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn btn-secondary\",\n              onClick: () => navigate('/upload'),\n              children: \"Back\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 588,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"btn btn-primary\",\n              children: \"Preview Book\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 587,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 242,\n    columnNumber: 5\n  }, this);\n};\nexport default FormatSettings;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useLocation", "axios", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "FormatSettings", "_location$state", "_trimSizeOptions$find", "_paperTypeOptions$fin", "navigate", "location", "documentId", "state", "loading", "setLoading", "error", "setError", "documentInfo", "setDocumentInfo", "settings", "setSettings", "trimSize", "pageColor", "marginTop", "marginBottom", "marginInside", "marginOutside", "hasBleed", "paperType", "pageNumbering", "pageNumberPosition", "fontName", "fontSize", "lineSpacing", "includeTOC", "includeChapterBreaks", "trimSizeOptions", "value", "label", "description", "paperTypeOptions", "fontOptions", "gutterSizeByPageCount", "maxPages", "minGutterSize", "getMinGutterSize", "pageCount", "sizeRule", "fetchDocumentInfo", "response", "get", "data", "recommendedSettings", "prevSettings", "err", "console", "handleChange", "e", "name", "type", "checked", "target", "newSettings", "Math", "max", "includeSpineText", "handleNumericChange", "min", "numValue", "parseFloat", "isNaN", "actualMin", "handleSubmit", "preventDefault", "post", "formatId", "_err$response", "_err$response$data", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "wordCount", "toLocaleString", "fileFormat", "onSubmit", "htmlFor", "content", "id", "onChange", "map", "option", "find", "o", "step", "document"], "sources": ["C:/Users/<USER>/Desktop/New folder/My Project/KDP/frontend/src/pages/FormatSettings.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useNavigate, useLocation } from 'react-router-dom';\r\nimport axios from 'axios';\r\nimport './FormatSettings.css';\r\nimport Tooltip from '../components/common/Tooltip';\r\n\r\nconst FormatSettings = () => {\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n  const documentId = location.state?.documentId;\r\n  \r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [documentInfo, setDocumentInfo] = useState(null);\r\n  \r\n  // Format settings\r\n  const [settings, setSettings] = useState({\r\n    trimSize: '6x9',\r\n    pageColor: 'white',\r\n    marginTop: 1,\r\n    marginBottom: 1,\r\n    marginInside: 0.75,\r\n    marginOutside: 0.75,\r\n    hasBleed: false,\r\n    paperType: 'white',\r\n    pageNumbering: true,\r\n    pageNumberPosition: 'bottom-center',\r\n    fontName: 'Times New Roman',\r\n    fontSize: 12,\r\n    lineSpacing: 1.5,\r\n    includeTOC: true,\r\n    includeChapterBreaks: true\r\n  });\r\n\r\n  // Available options based on KDP guidelines based on KDP guidelines\r\n  const trimSizeOptions = [\r\n    { value: '5x8', label: '5\" x 8\" (12.7 x 20.32 cm)', description: 'Common for fiction, novellas, short non-fiction' },\r\n    { value: '5.5x8.5', label: '5.5\" x 8.5\" (13.97 x 21.59 cm)', description: 'Popular size for fiction and non-fiction' },\r\n    { value: '6x9', label: '6\" x 9\" (15.24 x 22.86 cm)', description: 'Standard for textbooks and larger novels (KDP recommended)' },\r\n    { value: '7x10', label: '7\" x 10\" (17.78 x 25.4 cm)', description: 'Good for workbooks and textbooks' },\r\n    { value: '8.5x11', label: '8.5\" x 11\" (21.59 x 27.94 cm)', description: 'Letter size, good for large-format books' }\r\n    // KDP allows custom sizes within 4\"–8.5\" width and 6\"–11.69\" height\r\n    // KDP allows custom sizes within 4\"–8.5\" width and 6\"–11.69\" height\r\n  ];\r\n\r\n  const paperTypeOptions = [\r\n    { value: 'white', label: 'White Paper', description: 'Bright white paper, good contrast for text and images' },\r\n    { value: 'cream', label: 'Cream Paper', description: 'Warmer tone, easier on the eyes, popular for fiction (only available for black & white interior) (only available for black & white interior)' }\r\n  ];\r\n\r\n  const fontOptions = [\r\n    { value: 'Times New Roman', label: 'Times New Roman', description: 'Classic serif font, very readable' },\r\n    { value: 'Garamond', label: 'Garamond', description: 'Elegant serif font with traditional look' },\r\n    { value: 'Georgia', label: 'Georgia', description: 'Clear, readable serif designed for screens' },\r\n    { value: 'Arial', label: 'Arial', description: 'Clean sans-serif font' },\r\n    { value: 'Helvetica', label: 'Helvetica', description: 'Modern sans-serif with neutral appearance' },\r\n    { value: 'Baskerville', label: 'Baskerville', description: 'Traditional serif with good readability' }\r\n  ];\r\n  \r\n  // KDP gutter size requirements based on page count\r\n  const gutterSizeByPageCount = [\r\n    { maxPages: 150, minGutterSize: 0.375 },\r\n    { maxPages: 300, minGutterSize: 0.5 },\r\n    { maxPages: 500, minGutterSize: 0.625 },\r\n    { maxPages: 700, minGutterSize: 0.75 },\r\n    { maxPages: 828, minGutterSize: 0.875 }\r\n  ];\r\n  \r\n  // Get minimum required gutter size based on page count\r\n  const getMinGutterSize = (pageCount) => {\r\n    for (const sizeRule of gutterSizeByPageCount) {\r\n      if (pageCount <= sizeRule.maxPages) {\r\n        return sizeRule.minGutterSize;\r\n      }\r\n    }\r\n    return 0.875; // Default to largest size for very large books\r\n  };\r\n  \r\n\r\n\r\n  // Fetch document info\r\n  useEffect(() => {\r\n    if (!documentId) {\r\n      navigate('/upload');\r\n      return;\r\n    }\r\n\r\n    const fetchDocumentInfo = async () => {\r\n      try {\r\n        setLoading(true);\r\n        const response = await axios.get(`/api/document/${documentId}`);\r\n        setDocumentInfo(response.data);\r\n        \r\n        // Set initial settings based on document info if available\r\n        if (response.data.recommendedSettings) {\r\n          setSettings(prevSettings => ({\r\n            ...prevSettings,\r\n            ...response.data.recommendedSettings\r\n          }));\r\n        }\r\n      } catch (err) {\r\n        console.error('Error fetching document info:', err);\r\n        setError('Failed to load document information. Please try again.');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchDocumentInfo();\r\n  }, [documentId, navigate]);\r\n\r\n  // Handle form field changes\r\n  const handleChange = (e) => {\r\n    const { name, value, type, checked } = e.target;\r\n    \r\n    // Update settings and handle dependent values\r\n    setSettings(prevSettings => {\r\n      const newSettings = {\r\n        ...prevSettings,\r\n        [name]: type === 'checkbox' ? checked : value\r\n      };\r\n      \r\n      // Special handling for dependent settings\r\n      if (name === 'hasBleed' && checked) {\r\n        // When enabling bleed, ensure margins meet minimum requirements\r\n        newSettings.marginTop = Math.max(newSettings.marginTop, 0.375);\r\n        newSettings.marginBottom = Math.max(newSettings.marginBottom, 0.375);\r\n        newSettings.marginOutside = Math.max(newSettings.marginOutside, 0.375);\r\n        newSettings.marginInside = Math.max(newSettings.marginInside, getMinGutterSize(documentInfo?.pageCount || 150));\r\n      }\r\n      \r\n      // When changing page color, handle paper type compatibility\r\n      if (name === 'pageColor' && value === 'color') {\r\n        // Color interior can only use white paper\r\n        newSettings.paperType = 'white';\r\n      }\r\n      \r\n      // When page count is very low, disable spine text\r\n      if ((name === 'documentId' || name === 'pageCount') && documentInfo?.pageCount < 79) {\r\n        newSettings.includeSpineText = false;\r\n      }\r\n      \r\n      return newSettings;\r\n    });\r\n  };\r\n\r\n  // Handle numeric input with limits, accounting for KDP requirements\r\n  const handleNumericChange = (e, min, max) => {\r\n    const { name, value } = e.target;\r\n    const numValue = parseFloat(value);\r\n    \r\n    if (isNaN(numValue)) {\r\n      setSettings(prevSettings => ({\r\n        ...prevSettings,\r\n        [name]: ''\r\n      }));\r\n      return;\r\n    }\r\n    \r\n    // Get minimum value based on KDP requirements\r\n    let actualMin = min;\r\n    if (name === 'marginInside') {\r\n      // Inside margin (gutter) has special minimum based on page count\r\n      actualMin = getMinGutterSize(documentInfo?.pageCount || 150);\r\n    } else if (settings.hasBleed && (name === 'marginTop' || name === 'marginBottom' || name === 'marginOutside')) {\r\n      // For bleed documents, outside/top/bottom margins must be at least 0.375\"\r\n      actualMin = Math.max(min, 0.375);\r\n    } else if (!settings.hasBleed && (name === 'marginTop' || name === 'marginBottom' || name === 'marginOutside')) {\r\n      // For non-bleed documents, outside/top/bottom margins must be at least 0.25\"\r\n      actualMin = Math.max(min, 0.25);\r\n    }\r\n    \r\n    if (numValue < actualMin) {\r\n      setSettings(prevSettings => ({\r\n        ...prevSettings,\r\n        [name]: actualMin\r\n      }));\r\n    } else if (numValue > max) {\r\n      setSettings(prevSettings => ({\r\n        ...prevSettings,\r\n        [name]: max\r\n      }));\r\n    } else {\r\n      setSettings(prevSettings => ({\r\n        ...prevSettings,\r\n        [name]: numValue\r\n      }));\r\n    }\r\n  };\r\n\r\n  // Submit settings and navigate to preview\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    \r\n    try {\r\n      const response = await axios.post(`/api/document/${documentId}/settings`, settings);\r\n      navigate('/preview', { \r\n        state: { \r\n          documentId,\r\n          formatId: response.data.formatId \r\n        }\r\n      });\r\n    } catch (err) {\r\n      console.error('Error saving settings:', err);\r\n      setError(err.response?.data?.message || 'Failed to save formatting settings. Please try again.');\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"format-settings-page\">\r\n        <div className=\"container\">\r\n          <div className=\"loading-container\">\r\n            <div className=\"loading-spinner\"></div>\r\n            <p>Loading document information...</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"format-settings-page\">\r\n        <div className=\"container\">\r\n          <div className=\"error-container\">\r\n            <h2>Error</h2>\r\n            <p>{error}</p>\r\n            <button \r\n              onClick={() => navigate('/upload')} \r\n              className=\"btn btn-primary\"\r\n            >\r\n              Back to Upload\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"format-settings-page\">\r\n      <div className=\"container\">\r\n        <div className=\"format-settings-container\">\r\n          <h1>Format Your Book</h1>\r\n          \r\n          {documentInfo && (\r\n            <div className=\"document-info\">\r\n              <h3>Document Information</h3>\r\n              <div className=\"info-grid\">\r\n                <div className=\"info-item\">\r\n                  <span className=\"info-label\">File Name:</span>\r\n                  <span className=\"info-value\">{documentInfo.fileName}</span>\r\n                </div>\r\n                <div className=\"info-item\">\r\n                  <span className=\"info-label\">Pages:</span>\r\n                  <span className=\"info-value\">{documentInfo.pageCount}</span>\r\n                </div>\r\n                <div className=\"info-item\">\r\n                  <span className=\"info-label\">Word Count:</span>\r\n                  <span className=\"info-value\">{documentInfo.wordCount.toLocaleString()}</span>\r\n                </div>\r\n                <div className=\"info-item\">\r\n                  <span className=\"info-label\">File Format:</span>\r\n                  <span className=\"info-value\">{documentInfo.fileFormat}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n          \r\n          <form onSubmit={handleSubmit}>\r\n            <div className=\"settings-section\">\r\n              <h3>Book Size & Margins</h3>\r\n              \r\n              <div className=\"form-group\">\r\n                <label className=\"form-label\" htmlFor=\"trimSize\">\r\n                  Trim Size \r\n                  <Tooltip content=\"The physical dimensions of your book. The 6×9 inch size is the most cost-effective option for most books.\" />\r\n                </label>\r\n                <select\r\n                  id=\"trimSize\"\r\n                  name=\"trimSize\"\r\n                  value={settings.trimSize}\r\n                  onChange={handleChange}\r\n                  className=\"form-control\"\r\n                >\r\n                  {trimSizeOptions.map(option => (\r\n                    <option key={option.value} value={option.value}>\r\n                      {option.label}\r\n                    </option>\r\n                  ))}\r\n                </select>\r\n                <p className=\"form-hint\">{trimSizeOptions.find(o => o.value === settings.trimSize)?.description}</p>\r\n              </div>\r\n              \r\n              <div className=\"form-group\">\r\n                <label className=\"form-label\">\r\n                  Margins \r\n                  <Tooltip content=\"Margins are the white space around your text. Inside margins need to be larger to account for binding.\" />\r\n                </label>\r\n                <div className=\"margins-grid\">\r\n                  <div>\r\n                    <label htmlFor=\"marginTop\">Top (inches)</label>\r\n                    <input\r\n                      id=\"marginTop\"\r\n                      name=\"marginTop\"\r\n                      type=\"number\"\r\n                      step=\"0.1\"\r\n                      min=\"0.5\"\r\n                      max=\"3\"\r\n                      value={settings.marginTop}\r\n                      onChange={(e) => handleNumericChange(e, 0.5, 3)}\r\n                      className=\"form-control\"\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label htmlFor=\"marginBottom\">Bottom (inches)</label>\r\n                    <input\r\n                      id=\"marginBottom\"\r\n                      name=\"marginBottom\"\r\n                      type=\"number\"\r\n                      step=\"0.1\"\r\n                      min=\"0.5\"\r\n                      max=\"3\"\r\n                      value={settings.marginBottom}\r\n                      onChange={(e) => handleNumericChange(e, 0.5, 3)}\r\n                      className=\"form-control\"\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label htmlFor=\"marginInside\">Inside (inches)</label>\r\n                    <input\r\n                      id=\"marginInside\"\r\n                      name=\"marginInside\"\r\n                      type=\"number\"\r\n                      step=\"0.1\"\r\n                      min=\"0.5\"\r\n                      max=\"3\"\r\n                      value={settings.marginInside}\r\n                      onChange={(e) => handleNumericChange(e, 0.5, 3)}\r\n                      className=\"form-control\"\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label htmlFor=\"marginOutside\">Outside (inches)</label>\r\n                    <input\r\n                      id=\"marginOutside\"\r\n                      name=\"marginOutside\"\r\n                      type=\"number\"\r\n                      step=\"0.1\"\r\n                      min=\"0.5\"\r\n                      max=\"3\"\r\n                      value={settings.marginOutside}\r\n                      onChange={(e) => handleNumericChange(e, 0.5, 3)}\r\n                      className=\"form-control\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              \r\n              <div className=\"form-group\">\r\n                <div className=\"checkbox-group\">\r\n                  <input\r\n                    id=\"hasBleed\"\r\n                    name=\"hasBleed\"\r\n                    type=\"checkbox\"\r\n                    checked={settings.hasBleed}\r\n                    onChange={handleChange}\r\n                  />\r\n                  <label htmlFor=\"hasBleed\">\r\n                    Include bleed \r\n                    <Tooltip content=\"Only enable bleed if your book contains images that extend to the edge of the page. This adds 0.125 inches to the outside edges.\" />\r\n                  </label>\r\n                </div>\r\n                {settings.hasBleed && (\r\n                  <div className=\"bleed-warning\">\r\n                    <p>\r\n                      <strong>Important:</strong> KDP requires PDF format for books with bleed. \r\n                      {document.fileFormat !== 'PDF' && \" Your document will be converted to PDF.\"}\r\n                    </p>\r\n                    <p className=\"small-text\">\r\n                      Bleed area (0.125\" on top, bottom, and outside edge) will be trimmed during printing. \r\n                      Make sure all important content stays within the safe zone.\r\n                    </p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n            \r\n            <div className=\"settings-section\">\r\n              <h3>Paper & Appearance</h3>\r\n              \r\n              <div className=\"form-group\">\r\n                <label className=\"form-label\" htmlFor=\"paperType\">\r\n                  Paper Type \r\n                  <Tooltip content=\"White paper provides better contrast for text and images. Cream paper is easier on the eyes for extended reading.\" />\r\n                </label>\r\n                <select\r\n                  id=\"paperType\"\r\n                  name=\"paperType\"\r\n                  value={settings.paperType}\r\n                  onChange={handleChange}\r\n                  className=\"form-control\"\r\n                >\r\n                  {paperTypeOptions.map(option => (\r\n                    <option key={option.value} value={option.value}>\r\n                      {option.label}\r\n                    </option>\r\n                  ))}\r\n                </select>\r\n                <p className=\"form-hint\">{paperTypeOptions.find(o => o.value === settings.paperType)?.description}</p>\r\n                {settings.pageColor === 'color' && (\r\n                  <div className=\"color-note\">\r\n                    <p className=\"small-text\">\r\n                      <strong>Note:</strong> Color interiors are only available with white paper.\r\n                      {settings.paperType === 'cream' && ' Your paper type has been changed to white.'}\r\n                    </p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n              \r\n              <div className=\"form-group\">\r\n                <label className=\"form-label\" htmlFor=\"pageColor\">\r\n                  Interior Color \r\n                  <Tooltip content=\"Black & white is more affordable. Color is best for books with photos or colored illustrations.\" />\r\n                </label>\r\n                <select\r\n                  id=\"pageColor\"\r\n                  name=\"pageColor\"\r\n                  value={settings.pageColor}\r\n                  onChange={handleChange}\r\n                  className=\"form-control\"\r\n                >\r\n                  <option value=\"white\">Black & White</option>\r\n                  <option value=\"color\">Full Color (Higher Cost)</option>\r\n                </select>\r\n                {settings.pageColor === 'color' && (\r\n                  <div className=\"color-note\">\r\n                    <p className=\"small-text\">\r\n                      <strong>Note:</strong> Color interiors are only available with white paper.\r\n                      {settings.paperType === 'cream' && ' Your paper type has been changed to white.'}\r\n                    </p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n            \r\n            <div className=\"settings-section\">\r\n              <h3>Text Formatting</h3>\r\n              \r\n              <div className=\"form-group\">\r\n                <label className=\"form-label\" htmlFor=\"fontName\">\r\n                  Font \r\n                  <Tooltip content=\"Choose a font that's easy to read. Serif fonts (like Times New Roman) are traditional for books.\" />\r\n                </label>\r\n                <select\r\n                  id=\"fontName\"\r\n                  name=\"fontName\"\r\n                  value={settings.fontName}\r\n                  onChange={handleChange}\r\n                  className=\"form-control\"\r\n                >\r\n                  {fontOptions.map(option => (\r\n                    <option key={option.value} value={option.value}>\r\n                      {option.label}\r\n                    </option>\r\n                  ))}\r\n                </select>\r\n              </div>\r\n              \r\n              <div className=\"form-row\">\r\n                <div className=\"form-group\">\r\n                  <label className=\"form-label\" htmlFor=\"fontSize\">\r\n                    Font Size \r\n                    <Tooltip content=\"11-12pt is standard for books. Larger sizes are easier to read but use more pages.\" />\r\n                  </label>\r\n                  <select\r\n                    id=\"fontSize\"\r\n                    name=\"fontSize\"\r\n                    value={settings.fontSize}\r\n                    onChange={handleChange}\r\n                    className=\"form-control\"\r\n                  >\r\n                    <option value=\"10\">10pt (Small)</option>\r\n                    <option value=\"11\">11pt</option>\r\n                    <option value=\"12\">12pt (Standard)</option>\r\n                    <option value=\"14\">14pt (Larger)</option>\r\n                    <option value=\"16\">16pt (Very Large)</option>\r\n                  </select>\r\n                </div>\r\n                \r\n                <div className=\"form-group\">\r\n                  <label className=\"form-label\" htmlFor=\"lineSpacing\">\r\n                    Line Spacing \r\n                    <Tooltip content=\"The space between lines of text. 1.5 is standard for easy reading.\" />\r\n                  </label>\r\n                  <select\r\n                    id=\"lineSpacing\"\r\n                    name=\"lineSpacing\"\r\n                    value={settings.lineSpacing}\r\n                    onChange={handleChange}\r\n                    className=\"form-control\"\r\n                  >\r\n                    <option value=\"1\">Single (1.0)</option>\r\n                    <option value=\"1.15\">1.15</option>\r\n                    <option value=\"1.5\">1.5 (Standard)</option>\r\n                    <option value=\"2\">Double (2.0)</option>\r\n                  </select>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            \r\n            <div className=\"settings-section\">\r\n              <h3>Book Structure</h3>\r\n              \r\n              <div className=\"form-group\">\r\n                <div className=\"checkbox-group\">\r\n                  <input\r\n                    id=\"pageNumbering\"\r\n                    name=\"pageNumbering\"\r\n                    type=\"checkbox\"\r\n                    checked={settings.pageNumbering}\r\n                    onChange={handleChange}\r\n                  />\r\n                  <label htmlFor=\"pageNumbering\">\r\n                    Add page numbers \r\n                    <Tooltip content=\"Page numbers are recommended for all books.\" />\r\n                  </label>\r\n                </div>\r\n              </div>\r\n              \r\n              {settings.pageNumbering && (\r\n                <div className=\"form-group\">\r\n                  <label className=\"form-label\" htmlFor=\"pageNumberPosition\">\r\n                    Page Number Position\r\n                  </label>\r\n                  <select\r\n                    id=\"pageNumberPosition\"\r\n                    name=\"pageNumberPosition\"\r\n                    value={settings.pageNumberPosition}\r\n                    onChange={handleChange}\r\n                    className=\"form-control\"\r\n                  >\r\n                    <option value=\"bottom-center\">Bottom Center</option>\r\n                    <option value=\"bottom-outside\">Bottom Outside</option>\r\n                    <option value=\"top-center\">Top Center</option>\r\n                    <option value=\"top-outside\">Top Outside</option>\r\n                  </select>\r\n                </div>\r\n              )}\r\n              \r\n              <div className=\"form-group\">\r\n                <div className=\"checkbox-group\">\r\n                  <input\r\n                    id=\"includeTOC\"\r\n                    name=\"includeTOC\"\r\n                    type=\"checkbox\"\r\n                    checked={settings.includeTOC}\r\n                    onChange={handleChange}\r\n                  />\r\n                  <label htmlFor=\"includeTOC\">\r\n                    Generate Table of Contents \r\n                    <Tooltip content=\"Automatically create a table of contents based on your chapter headings.\" />\r\n                  </label>\r\n                </div>\r\n              </div>\r\n              \r\n              <div className=\"form-group\">\r\n                <div className=\"checkbox-group\">\r\n                  <input\r\n                    id=\"includeChapterBreaks\"\r\n                    name=\"includeChapterBreaks\"\r\n                    type=\"checkbox\"\r\n                    checked={settings.includeChapterBreaks}\r\n                    onChange={handleChange}\r\n                  />\r\n                  <label htmlFor=\"includeChapterBreaks\">\r\n                    Force chapters to start on right pages \r\n                    <Tooltip content=\"Professional books typically start each chapter on a right-facing (odd-numbered) page.\" />\r\n                  </label>\r\n                </div>\r\n              </div>\r\n              \r\n\r\n            </div>\r\n            \r\n            <div className=\"form-actions\">\r\n              <button type=\"button\" className=\"btn btn-secondary\" onClick={() => navigate('/upload')}>\r\n                Back\r\n              </button>\r\n              <button type=\"submit\" className=\"btn btn-primary\">\r\n                Preview Book\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FormatSettings;"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,sBAAsB;AAC7B,OAAOC,OAAO,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAA,IAAAC,eAAA,EAAAC,qBAAA,EAAAC,qBAAA;EAC3B,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,UAAU,IAAAL,eAAA,GAAGI,QAAQ,CAACE,KAAK,cAAAN,eAAA,uBAAdA,eAAA,CAAgBK,UAAU;EAE7C,MAAM,CAACE,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;;EAEtD;EACA,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC;IACvCwB,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE,OAAO;IAClBC,SAAS,EAAE,CAAC;IACZC,YAAY,EAAE,CAAC;IACfC,YAAY,EAAE,IAAI;IAClBC,aAAa,EAAE,IAAI;IACnBC,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE,OAAO;IAClBC,aAAa,EAAE,IAAI;IACnBC,kBAAkB,EAAE,eAAe;IACnCC,QAAQ,EAAE,iBAAiB;IAC3BC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,GAAG;IAChBC,UAAU,EAAE,IAAI;IAChBC,oBAAoB,EAAE;EACxB,CAAC,CAAC;;EAEF;EACA,MAAMC,eAAe,GAAG,CACtB;IAAEC,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE,2BAA2B;IAAEC,WAAW,EAAE;EAAkD,CAAC,EACpH;IAAEF,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,gCAAgC;IAAEC,WAAW,EAAE;EAA2C,CAAC,EACtH;IAAEF,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE,4BAA4B;IAAEC,WAAW,EAAE;EAA6D,CAAC,EAChI;IAAEF,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE,4BAA4B;IAAEC,WAAW,EAAE;EAAmC,CAAC,EACvG;IAAEF,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE,+BAA+B;IAAEC,WAAW,EAAE;EAA2C;EACnH;EACA;EAAA,CACD;EAED,MAAMC,gBAAgB,GAAG,CACvB;IAAEH,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE,aAAa;IAAEC,WAAW,EAAE;EAAwD,CAAC,EAC9G;IAAEF,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE,aAAa;IAAEC,WAAW,EAAE;EAA+I,CAAC,CACtM;EAED,MAAME,WAAW,GAAG,CAClB;IAAEJ,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,WAAW,EAAE;EAAoC,CAAC,EACxG;IAAEF,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE,UAAU;IAAEC,WAAW,EAAE;EAA2C,CAAC,EACjG;IAAEF,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEC,WAAW,EAAE;EAA6C,CAAC,EACjG;IAAEF,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE,OAAO;IAAEC,WAAW,EAAE;EAAwB,CAAC,EACxE;IAAEF,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE,WAAW;IAAEC,WAAW,EAAE;EAA4C,CAAC,EACpG;IAAEF,KAAK,EAAE,aAAa;IAAEC,KAAK,EAAE,aAAa;IAAEC,WAAW,EAAE;EAA0C,CAAC,CACvG;;EAED;EACA,MAAMG,qBAAqB,GAAG,CAC5B;IAAEC,QAAQ,EAAE,GAAG;IAAEC,aAAa,EAAE;EAAM,CAAC,EACvC;IAAED,QAAQ,EAAE,GAAG;IAAEC,aAAa,EAAE;EAAI,CAAC,EACrC;IAAED,QAAQ,EAAE,GAAG;IAAEC,aAAa,EAAE;EAAM,CAAC,EACvC;IAAED,QAAQ,EAAE,GAAG;IAAEC,aAAa,EAAE;EAAK,CAAC,EACtC;IAAED,QAAQ,EAAE,GAAG;IAAEC,aAAa,EAAE;EAAM,CAAC,CACxC;;EAED;EACA,MAAMC,gBAAgB,GAAIC,SAAS,IAAK;IACtC,KAAK,MAAMC,QAAQ,IAAIL,qBAAqB,EAAE;MAC5C,IAAII,SAAS,IAAIC,QAAQ,CAACJ,QAAQ,EAAE;QAClC,OAAOI,QAAQ,CAACH,aAAa;MAC/B;IACF;IACA,OAAO,KAAK,CAAC,CAAC;EAChB,CAAC;;EAID;EACA9C,SAAS,CAAC,MAAM;IACd,IAAI,CAACa,UAAU,EAAE;MACfF,QAAQ,CAAC,SAAS,CAAC;MACnB;IACF;IAEA,MAAMuC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC,IAAI;QACFlC,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMmC,QAAQ,GAAG,MAAMhD,KAAK,CAACiD,GAAG,CAAC,iBAAiBvC,UAAU,EAAE,CAAC;QAC/DO,eAAe,CAAC+B,QAAQ,CAACE,IAAI,CAAC;;QAE9B;QACA,IAAIF,QAAQ,CAACE,IAAI,CAACC,mBAAmB,EAAE;UACrChC,WAAW,CAACiC,YAAY,KAAK;YAC3B,GAAGA,YAAY;YACf,GAAGJ,QAAQ,CAACE,IAAI,CAACC;UACnB,CAAC,CAAC,CAAC;QACL;MACF,CAAC,CAAC,OAAOE,GAAG,EAAE;QACZC,OAAO,CAACxC,KAAK,CAAC,+BAA+B,EAAEuC,GAAG,CAAC;QACnDtC,QAAQ,CAAC,wDAAwD,CAAC;MACpE,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDkC,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACrC,UAAU,EAAEF,QAAQ,CAAC,CAAC;;EAE1B;EACA,MAAM+C,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEC,IAAI;MAAErB,KAAK;MAAEsB,IAAI;MAAEC;IAAQ,CAAC,GAAGH,CAAC,CAACI,MAAM;;IAE/C;IACAzC,WAAW,CAACiC,YAAY,IAAI;MAC1B,MAAMS,WAAW,GAAG;QAClB,GAAGT,YAAY;QACf,CAACK,IAAI,GAAGC,IAAI,KAAK,UAAU,GAAGC,OAAO,GAAGvB;MAC1C,CAAC;;MAED;MACA,IAAIqB,IAAI,KAAK,UAAU,IAAIE,OAAO,EAAE;QAClC;QACAE,WAAW,CAACvC,SAAS,GAAGwC,IAAI,CAACC,GAAG,CAACF,WAAW,CAACvC,SAAS,EAAE,KAAK,CAAC;QAC9DuC,WAAW,CAACtC,YAAY,GAAGuC,IAAI,CAACC,GAAG,CAACF,WAAW,CAACtC,YAAY,EAAE,KAAK,CAAC;QACpEsC,WAAW,CAACpC,aAAa,GAAGqC,IAAI,CAACC,GAAG,CAACF,WAAW,CAACpC,aAAa,EAAE,KAAK,CAAC;QACtEoC,WAAW,CAACrC,YAAY,GAAGsC,IAAI,CAACC,GAAG,CAACF,WAAW,CAACrC,YAAY,EAAEoB,gBAAgB,CAAC,CAAA5B,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE6B,SAAS,KAAI,GAAG,CAAC,CAAC;MACjH;;MAEA;MACA,IAAIY,IAAI,KAAK,WAAW,IAAIrB,KAAK,KAAK,OAAO,EAAE;QAC7C;QACAyB,WAAW,CAAClC,SAAS,GAAG,OAAO;MACjC;;MAEA;MACA,IAAI,CAAC8B,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,WAAW,KAAK,CAAAzC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE6B,SAAS,IAAG,EAAE,EAAE;QACnFgB,WAAW,CAACG,gBAAgB,GAAG,KAAK;MACtC;MAEA,OAAOH,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMI,mBAAmB,GAAGA,CAACT,CAAC,EAAEU,GAAG,EAAEH,GAAG,KAAK;IAC3C,MAAM;MAAEN,IAAI;MAAErB;IAAM,CAAC,GAAGoB,CAAC,CAACI,MAAM;IAChC,MAAMO,QAAQ,GAAGC,UAAU,CAAChC,KAAK,CAAC;IAElC,IAAIiC,KAAK,CAACF,QAAQ,CAAC,EAAE;MACnBhD,WAAW,CAACiC,YAAY,KAAK;QAC3B,GAAGA,YAAY;QACf,CAACK,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;MACH;IACF;;IAEA;IACA,IAAIa,SAAS,GAAGJ,GAAG;IACnB,IAAIT,IAAI,KAAK,cAAc,EAAE;MAC3B;MACAa,SAAS,GAAG1B,gBAAgB,CAAC,CAAA5B,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE6B,SAAS,KAAI,GAAG,CAAC;IAC9D,CAAC,MAAM,IAAI3B,QAAQ,CAACQ,QAAQ,KAAK+B,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,cAAc,IAAIA,IAAI,KAAK,eAAe,CAAC,EAAE;MAC7G;MACAa,SAAS,GAAGR,IAAI,CAACC,GAAG,CAACG,GAAG,EAAE,KAAK,CAAC;IAClC,CAAC,MAAM,IAAI,CAAChD,QAAQ,CAACQ,QAAQ,KAAK+B,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,cAAc,IAAIA,IAAI,KAAK,eAAe,CAAC,EAAE;MAC9G;MACAa,SAAS,GAAGR,IAAI,CAACC,GAAG,CAACG,GAAG,EAAE,IAAI,CAAC;IACjC;IAEA,IAAIC,QAAQ,GAAGG,SAAS,EAAE;MACxBnD,WAAW,CAACiC,YAAY,KAAK;QAC3B,GAAGA,YAAY;QACf,CAACK,IAAI,GAAGa;MACV,CAAC,CAAC,CAAC;IACL,CAAC,MAAM,IAAIH,QAAQ,GAAGJ,GAAG,EAAE;MACzB5C,WAAW,CAACiC,YAAY,KAAK;QAC3B,GAAGA,YAAY;QACf,CAACK,IAAI,GAAGM;MACV,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACL5C,WAAW,CAACiC,YAAY,KAAK;QAC3B,GAAGA,YAAY;QACf,CAACK,IAAI,GAAGU;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC;;EAED;EACA,MAAMI,YAAY,GAAG,MAAOf,CAAC,IAAK;IAChCA,CAAC,CAACgB,cAAc,CAAC,CAAC;IAElB,IAAI;MACF,MAAMxB,QAAQ,GAAG,MAAMhD,KAAK,CAACyE,IAAI,CAAC,iBAAiB/D,UAAU,WAAW,EAAEQ,QAAQ,CAAC;MACnFV,QAAQ,CAAC,UAAU,EAAE;QACnBG,KAAK,EAAE;UACLD,UAAU;UACVgE,QAAQ,EAAE1B,QAAQ,CAACE,IAAI,CAACwB;QAC1B;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOrB,GAAG,EAAE;MAAA,IAAAsB,aAAA,EAAAC,kBAAA;MACZtB,OAAO,CAACxC,KAAK,CAAC,wBAAwB,EAAEuC,GAAG,CAAC;MAC5CtC,QAAQ,CAAC,EAAA4D,aAAA,GAAAtB,GAAG,CAACL,QAAQ,cAAA2B,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAczB,IAAI,cAAA0B,kBAAA,uBAAlBA,kBAAA,CAAoBC,OAAO,KAAI,uDAAuD,CAAC;IAClG;EACF,CAAC;EAED,IAAIjE,OAAO,EAAE;IACX,oBACET,OAAA;MAAK2E,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACnC5E,OAAA;QAAK2E,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB5E,OAAA;UAAK2E,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC5E,OAAA;YAAK2E,SAAS,EAAC;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvChF,OAAA;YAAA4E,QAAA,EAAG;UAA+B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIrE,KAAK,EAAE;IACT,oBACEX,OAAA;MAAK2E,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACnC5E,OAAA;QAAK2E,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB5E,OAAA;UAAK2E,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B5E,OAAA;YAAA4E,QAAA,EAAI;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACdhF,OAAA;YAAA4E,QAAA,EAAIjE;UAAK;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACdhF,OAAA;YACEiF,OAAO,EAAEA,CAAA,KAAM5E,QAAQ,CAAC,SAAS,CAAE;YACnCsE,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC5B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEhF,OAAA;IAAK2E,SAAS,EAAC,sBAAsB;IAAAC,QAAA,eACnC5E,OAAA;MAAK2E,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxB5E,OAAA;QAAK2E,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxC5E,OAAA;UAAA4E,QAAA,EAAI;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAExBnE,YAAY,iBACXb,OAAA;UAAK2E,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B5E,OAAA;YAAA4E,QAAA,EAAI;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7BhF,OAAA;YAAK2E,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB5E,OAAA;cAAK2E,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB5E,OAAA;gBAAM2E,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9ChF,OAAA;gBAAM2E,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAE/D,YAAY,CAACgE;cAAQ;gBAAAA,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACNhF,OAAA;cAAK2E,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB5E,OAAA;gBAAM2E,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1ChF,OAAA;gBAAM2E,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAE/D,YAAY,CAAC6B;cAAS;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,eACNhF,OAAA;cAAK2E,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB5E,OAAA;gBAAM2E,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/ChF,OAAA;gBAAM2E,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAE/D,YAAY,CAACqE,SAAS,CAACC,cAAc,CAAC;cAAC;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC,eACNhF,OAAA;cAAK2E,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB5E,OAAA;gBAAM2E,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChDhF,OAAA;gBAAM2E,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAE/D,YAAY,CAACuE;cAAU;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDhF,OAAA;UAAMqF,QAAQ,EAAEjB,YAAa;UAAAQ,QAAA,gBAC3B5E,OAAA;YAAK2E,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/B5E,OAAA;cAAA4E,QAAA,EAAI;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAE5BhF,OAAA;cAAK2E,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB5E,OAAA;gBAAO2E,SAAS,EAAC,YAAY;gBAACW,OAAO,EAAC,UAAU;gBAAAV,QAAA,GAAC,WAE/C,eAAA5E,OAAA,CAACF,OAAO;kBAACyF,OAAO,EAAC;gBAA2G;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1H,CAAC,eACRhF,OAAA;gBACEwF,EAAE,EAAC,UAAU;gBACblC,IAAI,EAAC,UAAU;gBACfrB,KAAK,EAAElB,QAAQ,CAACE,QAAS;gBACzBwE,QAAQ,EAAErC,YAAa;gBACvBuB,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAEvB5C,eAAe,CAAC0D,GAAG,CAACC,MAAM,iBACzB3F,OAAA;kBAA2BiC,KAAK,EAAE0D,MAAM,CAAC1D,KAAM;kBAAA2C,QAAA,EAC5Ce,MAAM,CAACzD;gBAAK,GADFyD,MAAM,CAAC1D,KAAK;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEjB,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eACThF,OAAA;gBAAG2E,SAAS,EAAC,WAAW;gBAAAC,QAAA,GAAAzE,qBAAA,GAAE6B,eAAe,CAAC4D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5D,KAAK,KAAKlB,QAAQ,CAACE,QAAQ,CAAC,cAAAd,qBAAA,uBAAxDA,qBAAA,CAA0DgC;cAAW;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjG,CAAC,eAENhF,OAAA;cAAK2E,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB5E,OAAA;gBAAO2E,SAAS,EAAC,YAAY;gBAAAC,QAAA,GAAC,SAE5B,eAAA5E,OAAA,CAACF,OAAO;kBAACyF,OAAO,EAAC;gBAAwG;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvH,CAAC,eACRhF,OAAA;gBAAK2E,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B5E,OAAA;kBAAA4E,QAAA,gBACE5E,OAAA;oBAAOsF,OAAO,EAAC,WAAW;oBAAAV,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC/ChF,OAAA;oBACEwF,EAAE,EAAC,WAAW;oBACdlC,IAAI,EAAC,WAAW;oBAChBC,IAAI,EAAC,QAAQ;oBACbuC,IAAI,EAAC,KAAK;oBACV/B,GAAG,EAAC,KAAK;oBACTH,GAAG,EAAC,GAAG;oBACP3B,KAAK,EAAElB,QAAQ,CAACI,SAAU;oBAC1BsE,QAAQ,EAAGpC,CAAC,IAAKS,mBAAmB,CAACT,CAAC,EAAE,GAAG,EAAE,CAAC,CAAE;oBAChDsB,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNhF,OAAA;kBAAA4E,QAAA,gBACE5E,OAAA;oBAAOsF,OAAO,EAAC,cAAc;oBAAAV,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACrDhF,OAAA;oBACEwF,EAAE,EAAC,cAAc;oBACjBlC,IAAI,EAAC,cAAc;oBACnBC,IAAI,EAAC,QAAQ;oBACbuC,IAAI,EAAC,KAAK;oBACV/B,GAAG,EAAC,KAAK;oBACTH,GAAG,EAAC,GAAG;oBACP3B,KAAK,EAAElB,QAAQ,CAACK,YAAa;oBAC7BqE,QAAQ,EAAGpC,CAAC,IAAKS,mBAAmB,CAACT,CAAC,EAAE,GAAG,EAAE,CAAC,CAAE;oBAChDsB,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNhF,OAAA;kBAAA4E,QAAA,gBACE5E,OAAA;oBAAOsF,OAAO,EAAC,cAAc;oBAAAV,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACrDhF,OAAA;oBACEwF,EAAE,EAAC,cAAc;oBACjBlC,IAAI,EAAC,cAAc;oBACnBC,IAAI,EAAC,QAAQ;oBACbuC,IAAI,EAAC,KAAK;oBACV/B,GAAG,EAAC,KAAK;oBACTH,GAAG,EAAC,GAAG;oBACP3B,KAAK,EAAElB,QAAQ,CAACM,YAAa;oBAC7BoE,QAAQ,EAAGpC,CAAC,IAAKS,mBAAmB,CAACT,CAAC,EAAE,GAAG,EAAE,CAAC,CAAE;oBAChDsB,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNhF,OAAA;kBAAA4E,QAAA,gBACE5E,OAAA;oBAAOsF,OAAO,EAAC,eAAe;oBAAAV,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACvDhF,OAAA;oBACEwF,EAAE,EAAC,eAAe;oBAClBlC,IAAI,EAAC,eAAe;oBACpBC,IAAI,EAAC,QAAQ;oBACbuC,IAAI,EAAC,KAAK;oBACV/B,GAAG,EAAC,KAAK;oBACTH,GAAG,EAAC,GAAG;oBACP3B,KAAK,EAAElB,QAAQ,CAACO,aAAc;oBAC9BmE,QAAQ,EAAGpC,CAAC,IAAKS,mBAAmB,CAACT,CAAC,EAAE,GAAG,EAAE,CAAC,CAAE;oBAChDsB,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENhF,OAAA;cAAK2E,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB5E,OAAA;gBAAK2E,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7B5E,OAAA;kBACEwF,EAAE,EAAC,UAAU;kBACblC,IAAI,EAAC,UAAU;kBACfC,IAAI,EAAC,UAAU;kBACfC,OAAO,EAAEzC,QAAQ,CAACQ,QAAS;kBAC3BkE,QAAQ,EAAErC;gBAAa;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACFhF,OAAA;kBAAOsF,OAAO,EAAC,UAAU;kBAAAV,QAAA,GAAC,eAExB,eAAA5E,OAAA,CAACF,OAAO;oBAACyF,OAAO,EAAC;kBAAkI;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,EACLjE,QAAQ,CAACQ,QAAQ,iBAChBvB,OAAA;gBAAK2E,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B5E,OAAA;kBAAA4E,QAAA,gBACE5E,OAAA;oBAAA4E,QAAA,EAAQ;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,kDAC3B,EAACe,QAAQ,CAACX,UAAU,KAAK,KAAK,IAAI,0CAA0C;gBAAA;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E,CAAC,eACJhF,OAAA;kBAAG2E,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAG1B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhF,OAAA;YAAK2E,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/B5E,OAAA;cAAA4E,QAAA,EAAI;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAE3BhF,OAAA;cAAK2E,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB5E,OAAA;gBAAO2E,SAAS,EAAC,YAAY;gBAACW,OAAO,EAAC,WAAW;gBAAAV,QAAA,GAAC,YAEhD,eAAA5E,OAAA,CAACF,OAAO;kBAACyF,OAAO,EAAC;gBAAmH;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClI,CAAC,eACRhF,OAAA;gBACEwF,EAAE,EAAC,WAAW;gBACdlC,IAAI,EAAC,WAAW;gBAChBrB,KAAK,EAAElB,QAAQ,CAACS,SAAU;gBAC1BiE,QAAQ,EAAErC,YAAa;gBACvBuB,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAEvBxC,gBAAgB,CAACsD,GAAG,CAACC,MAAM,iBAC1B3F,OAAA;kBAA2BiC,KAAK,EAAE0D,MAAM,CAAC1D,KAAM;kBAAA2C,QAAA,EAC5Ce,MAAM,CAACzD;gBAAK,GADFyD,MAAM,CAAC1D,KAAK;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEjB,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eACThF,OAAA;gBAAG2E,SAAS,EAAC,WAAW;gBAAAC,QAAA,GAAAxE,qBAAA,GAAEgC,gBAAgB,CAACwD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5D,KAAK,KAAKlB,QAAQ,CAACS,SAAS,CAAC,cAAApB,qBAAA,uBAA1DA,qBAAA,CAA4D+B;cAAW;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACrGjE,QAAQ,CAACG,SAAS,KAAK,OAAO,iBAC7BlB,OAAA;gBAAK2E,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACzB5E,OAAA;kBAAG2E,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACvB5E,OAAA;oBAAA4E,QAAA,EAAQ;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,yDACtB,EAACjE,QAAQ,CAACS,SAAS,KAAK,OAAO,IAAI,6CAA6C;gBAAA;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENhF,OAAA;cAAK2E,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB5E,OAAA;gBAAO2E,SAAS,EAAC,YAAY;gBAACW,OAAO,EAAC,WAAW;gBAAAV,QAAA,GAAC,gBAEhD,eAAA5E,OAAA,CAACF,OAAO;kBAACyF,OAAO,EAAC;gBAAiG;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChH,CAAC,eACRhF,OAAA;gBACEwF,EAAE,EAAC,WAAW;gBACdlC,IAAI,EAAC,WAAW;gBAChBrB,KAAK,EAAElB,QAAQ,CAACG,SAAU;gBAC1BuE,QAAQ,EAAErC,YAAa;gBACvBuB,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAExB5E,OAAA;kBAAQiC,KAAK,EAAC,OAAO;kBAAA2C,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5ChF,OAAA;kBAAQiC,KAAK,EAAC,OAAO;kBAAA2C,QAAA,EAAC;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,EACRjE,QAAQ,CAACG,SAAS,KAAK,OAAO,iBAC7BlB,OAAA;gBAAK2E,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACzB5E,OAAA;kBAAG2E,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACvB5E,OAAA;oBAAA4E,QAAA,EAAQ;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,yDACtB,EAACjE,QAAQ,CAACS,SAAS,KAAK,OAAO,IAAI,6CAA6C;gBAAA;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhF,OAAA;YAAK2E,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/B5E,OAAA;cAAA4E,QAAA,EAAI;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAExBhF,OAAA;cAAK2E,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB5E,OAAA;gBAAO2E,SAAS,EAAC,YAAY;gBAACW,OAAO,EAAC,UAAU;gBAAAV,QAAA,GAAC,MAE/C,eAAA5E,OAAA,CAACF,OAAO;kBAACyF,OAAO,EAAC;gBAAkG;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjH,CAAC,eACRhF,OAAA;gBACEwF,EAAE,EAAC,UAAU;gBACblC,IAAI,EAAC,UAAU;gBACfrB,KAAK,EAAElB,QAAQ,CAACY,QAAS;gBACzB8D,QAAQ,EAAErC,YAAa;gBACvBuB,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAEvBvC,WAAW,CAACqD,GAAG,CAACC,MAAM,iBACrB3F,OAAA;kBAA2BiC,KAAK,EAAE0D,MAAM,CAAC1D,KAAM;kBAAA2C,QAAA,EAC5Ce,MAAM,CAACzD;gBAAK,GADFyD,MAAM,CAAC1D,KAAK;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEjB,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENhF,OAAA;cAAK2E,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB5E,OAAA;gBAAK2E,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB5E,OAAA;kBAAO2E,SAAS,EAAC,YAAY;kBAACW,OAAO,EAAC,UAAU;kBAAAV,QAAA,GAAC,WAE/C,eAAA5E,OAAA,CAACF,OAAO;oBAACyF,OAAO,EAAC;kBAAoF;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnG,CAAC,eACRhF,OAAA;kBACEwF,EAAE,EAAC,UAAU;kBACblC,IAAI,EAAC,UAAU;kBACfrB,KAAK,EAAElB,QAAQ,CAACa,QAAS;kBACzB6D,QAAQ,EAAErC,YAAa;kBACvBuB,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAExB5E,OAAA;oBAAQiC,KAAK,EAAC,IAAI;oBAAA2C,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxChF,OAAA;oBAAQiC,KAAK,EAAC,IAAI;oBAAA2C,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChChF,OAAA;oBAAQiC,KAAK,EAAC,IAAI;oBAAA2C,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC3ChF,OAAA;oBAAQiC,KAAK,EAAC,IAAI;oBAAA2C,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACzChF,OAAA;oBAAQiC,KAAK,EAAC,IAAI;oBAAA2C,QAAA,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENhF,OAAA;gBAAK2E,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB5E,OAAA;kBAAO2E,SAAS,EAAC,YAAY;kBAACW,OAAO,EAAC,aAAa;kBAAAV,QAAA,GAAC,cAElD,eAAA5E,OAAA,CAACF,OAAO;oBAACyF,OAAO,EAAC;kBAAoE;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC,eACRhF,OAAA;kBACEwF,EAAE,EAAC,aAAa;kBAChBlC,IAAI,EAAC,aAAa;kBAClBrB,KAAK,EAAElB,QAAQ,CAACc,WAAY;kBAC5B4D,QAAQ,EAAErC,YAAa;kBACvBuB,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAExB5E,OAAA;oBAAQiC,KAAK,EAAC,GAAG;oBAAA2C,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACvChF,OAAA;oBAAQiC,KAAK,EAAC,MAAM;oBAAA2C,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClChF,OAAA;oBAAQiC,KAAK,EAAC,KAAK;oBAAA2C,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC3ChF,OAAA;oBAAQiC,KAAK,EAAC,GAAG;oBAAA2C,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhF,OAAA;YAAK2E,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/B5E,OAAA;cAAA4E,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAEvBhF,OAAA;cAAK2E,SAAS,EAAC,YAAY;cAAAC,QAAA,eACzB5E,OAAA;gBAAK2E,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7B5E,OAAA;kBACEwF,EAAE,EAAC,eAAe;kBAClBlC,IAAI,EAAC,eAAe;kBACpBC,IAAI,EAAC,UAAU;kBACfC,OAAO,EAAEzC,QAAQ,CAACU,aAAc;kBAChCgE,QAAQ,EAAErC;gBAAa;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACFhF,OAAA;kBAAOsF,OAAO,EAAC,eAAe;kBAAAV,QAAA,GAAC,kBAE7B,eAAA5E,OAAA,CAACF,OAAO;oBAACyF,OAAO,EAAC;kBAA6C;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELjE,QAAQ,CAACU,aAAa,iBACrBzB,OAAA;cAAK2E,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB5E,OAAA;gBAAO2E,SAAS,EAAC,YAAY;gBAACW,OAAO,EAAC,oBAAoB;gBAAAV,QAAA,EAAC;cAE3D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRhF,OAAA;gBACEwF,EAAE,EAAC,oBAAoB;gBACvBlC,IAAI,EAAC,oBAAoB;gBACzBrB,KAAK,EAAElB,QAAQ,CAACW,kBAAmB;gBACnC+D,QAAQ,EAAErC,YAAa;gBACvBuB,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAExB5E,OAAA;kBAAQiC,KAAK,EAAC,eAAe;kBAAA2C,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpDhF,OAAA;kBAAQiC,KAAK,EAAC,gBAAgB;kBAAA2C,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtDhF,OAAA;kBAAQiC,KAAK,EAAC,YAAY;kBAAA2C,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9ChF,OAAA;kBAAQiC,KAAK,EAAC,aAAa;kBAAA2C,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,eAEDhF,OAAA;cAAK2E,SAAS,EAAC,YAAY;cAAAC,QAAA,eACzB5E,OAAA;gBAAK2E,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7B5E,OAAA;kBACEwF,EAAE,EAAC,YAAY;kBACflC,IAAI,EAAC,YAAY;kBACjBC,IAAI,EAAC,UAAU;kBACfC,OAAO,EAAEzC,QAAQ,CAACe,UAAW;kBAC7B2D,QAAQ,EAAErC;gBAAa;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACFhF,OAAA;kBAAOsF,OAAO,EAAC,YAAY;kBAAAV,QAAA,GAAC,4BAE1B,eAAA5E,OAAA,CAACF,OAAO;oBAACyF,OAAO,EAAC;kBAA0E;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENhF,OAAA;cAAK2E,SAAS,EAAC,YAAY;cAAAC,QAAA,eACzB5E,OAAA;gBAAK2E,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7B5E,OAAA;kBACEwF,EAAE,EAAC,sBAAsB;kBACzBlC,IAAI,EAAC,sBAAsB;kBAC3BC,IAAI,EAAC,UAAU;kBACfC,OAAO,EAAEzC,QAAQ,CAACgB,oBAAqB;kBACvC0D,QAAQ,EAAErC;gBAAa;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACFhF,OAAA;kBAAOsF,OAAO,EAAC,sBAAsB;kBAAAV,QAAA,GAAC,wCAEpC,eAAA5E,OAAA,CAACF,OAAO;oBAACyF,OAAO,EAAC;kBAAwF;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGH,CAAC,eAENhF,OAAA;YAAK2E,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B5E,OAAA;cAAQuD,IAAI,EAAC,QAAQ;cAACoB,SAAS,EAAC,mBAAmB;cAACM,OAAO,EAAEA,CAAA,KAAM5E,QAAQ,CAAC,SAAS,CAAE;cAAAuE,QAAA,EAAC;YAExF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThF,OAAA;cAAQuD,IAAI,EAAC,QAAQ;cAACoB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAElD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAED,eAAe/E,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}