{"version": 3, "file": "Keywords.js", "sourceRoot": "", "sources": ["../../../src/core/syntax/Keywords.ts"], "names": [], "mappings": ";;;;AAAA,kEAAkD;AAE1C,IAAA,KAAK,GAA8B,mBAAS,MAAvC,EAAE,cAAc,GAAc,mBAAS,eAAvB,EAAE,OAAO,GAAK,mBAAS,QAAd,CAAe;AAErD,IAAM,MAAM,GAAG;IACb,mBAAS,CAAC,CAAC;IACX,mBAAS,CAAC,CAAC;IACX,mBAAS,CAAC,CAAC;IACX,mBAAS,CAAC,CAAC;IACX,mBAAS,CAAC,CAAC;IACX,mBAAS,CAAC,CAAC;CACZ,CAAC;AAEF,IAAM,SAAS,GAAG;IAChB,mBAAS,CAAC,CAAC;IACX,mBAAS,CAAC,CAAC;IACX,mBAAS,CAAC,CAAC;IACX,mBAAS,CAAC,CAAC;IACX,mBAAS,CAAC,CAAC;IACX,mBAAS,CAAC,CAAC;IACX,mBAAS,CAAC,CAAC;IACX,mBAAS,CAAC,CAAC;IACX,mBAAS,CAAC,CAAC;CACZ,CAAC;AAEW,QAAA,QAAQ,GAAG;IACtB,MAAM,EAAE;QACN,mBAAS,CAAC,OAAO;QACjB,mBAAS,CAAC,CAAC;QACX,mBAAS,CAAC,CAAC;QACX,mBAAS,CAAC,CAAC;QACX,mBAAS,CAAC,IAAI;KACf;IACD,GAAG,EAAE;QACH,mBAAS,CAAC,OAAO;QACjB,mBAAS,CAAC,OAAO;QACjB,mBAAS,CAAC,CAAC;QACX,mBAAS,CAAC,CAAC;QACX,mBAAS,CAAC,CAAC;KACZ;IACD,GAAG,EAAE,CAAC,mBAAS,CAAC,CAAC,EAAE,mBAAS,CAAC,CAAC,EAAE,mBAAS,CAAC,CAAC,CAAC;IAC5C,MAAM,EAAE;QACN,mBAAS,CAAC,CAAC;QACX,mBAAS,CAAC,CAAC;QACX,mBAAS,CAAC,CAAC;QACX,mBAAS,CAAC,CAAC;QACX,mBAAS,CAAC,CAAC;QACX,mBAAS,CAAC,CAAC;KACZ;IACD,IAAI,EAAE,CAAC,mBAAS,CAAC,CAAC,EAAE,mBAAS,CAAC,CAAC,EAAE,mBAAS,CAAC,CAAC,EAAE,mBAAS,CAAC,CAAC,CAAC;IAC1D,OAAO,EAAE;QACP,mBAAS,CAAC,CAAC;QACX,mBAAS,CAAC,CAAC;QACX,mBAAS,CAAC,CAAC;QACX,mBAAS,CAAC,CAAC;QACX,mBAAS,CAAC,CAAC;QACX,mBAAS,CAAC,CAAC;QACX,mBAAS,CAAC,CAAC;KACZ;IACD,SAAS,EAAE;QACT,mBAAS,CAAC,CAAC;QACX,mBAAS,CAAC,CAAC;QACX,mBAAS,CAAC,CAAC;QACX,mBAAS,CAAC,CAAC;QACX,mBAAS,CAAC,CAAC;QACX,mBAAS,CAAC,CAAC;QACX,mBAAS,CAAC,CAAC;QACX,mBAAS,CAAC,CAAC;QACX,mBAAS,CAAC,CAAC;KACZ;IACD,IAAI,EAAE,CAAC,mBAAS,CAAC,CAAC,EAAE,mBAAS,CAAC,CAAC,EAAE,mBAAS,CAAC,CAAC,EAAE,mBAAS,CAAC,CAAC,CAAC;IAC1D,KAAK,EAAE,CAAC,mBAAS,CAAC,CAAC,EAAE,mBAAS,CAAC,CAAC,EAAE,mBAAS,CAAC,CAAC,EAAE,mBAAS,CAAC,CAAC,EAAE,mBAAS,CAAC,CAAC,CAAC;IACxE,IAAI,EAAE,CAAC,mBAAS,CAAC,CAAC,EAAE,mBAAS,CAAC,CAAC,EAAE,mBAAS,CAAC,CAAC,EAAE,mBAAS,CAAC,CAAC,CAAC;IAC1D,MAAM,QAAA;IACN,UAAU,yBAAM,MAAM,GAAE,KAAK,EAAE,cAAc,EAAE,OAAO,EAAC;IACvD,UAAU,yBAAM,MAAM,GAAE,cAAc,EAAE,OAAO,EAAC;IAChD,UAAU,yBAAM,MAAM,GAAE,cAAc,EAAC;IACvC,UAAU,yBAAM,MAAM,GAAE,OAAO,EAAC;IAChC,SAAS,WAAA;IACT,aAAa,0BAAG,cAAc,EAAE,OAAO,GAAK,SAAS,CAAC;IACtD,aAAa,0BAAG,cAAc,GAAK,SAAS,CAAC;IAC7C,aAAa,0BAAG,OAAO,GAAK,SAAS,CAAC;CACvC,CAAC"}