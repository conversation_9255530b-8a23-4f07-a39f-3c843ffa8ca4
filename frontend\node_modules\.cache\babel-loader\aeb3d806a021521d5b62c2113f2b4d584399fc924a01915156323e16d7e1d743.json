{"ast": null, "code": "import { __awaiter } from \"tslib\";\nimport { toFileWithPath } from './file';\nconst FILES_TO_IGNORE = [\n// Thumbnail cache files for macOS and Windows\n'.DS_Store',\n// macOs\n'Thumbs.db' // Windows\n];\n/**\n * Convert a DragEvent's DataTrasfer object to a list of File objects\n * NOTE: If some of the items are folders,\n * everything will be flattened and placed in the same list but the paths will be kept as a {path} property.\n *\n * EXPERIMENTAL: A list of https://developer.mozilla.org/en-US/docs/Web/API/FileSystemHandle objects can also be passed as an arg\n * and a list of File objects will be returned.\n *\n * @param evt\n */\nexport function fromEvent(evt) {\n  return __awaiter(this, void 0, void 0, function* () {\n    if (isObject(evt) && isDataTransfer(evt.dataTransfer)) {\n      return getDataTransferFiles(evt.dataTransfer, evt.type);\n    } else if (isChangeEvt(evt)) {\n      return getInputFiles(evt);\n    } else if (Array.isArray(evt) && evt.every(item => 'getFile' in item && typeof item.getFile === 'function')) {\n      return getFsHandleFiles(evt);\n    }\n    return [];\n  });\n}\nfunction isDataTransfer(value) {\n  return isObject(value);\n}\nfunction isChangeEvt(value) {\n  return isObject(value) && isObject(value.target);\n}\nfunction isObject(v) {\n  return typeof v === 'object' && v !== null;\n}\nfunction getInputFiles(evt) {\n  return fromList(evt.target.files).map(file => toFileWithPath(file));\n}\n// Ee expect each handle to be https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileHandle\nfunction getFsHandleFiles(handles) {\n  return __awaiter(this, void 0, void 0, function* () {\n    const files = yield Promise.all(handles.map(h => h.getFile()));\n    return files.map(file => toFileWithPath(file));\n  });\n}\nfunction getDataTransferFiles(dt, type) {\n  return __awaiter(this, void 0, void 0, function* () {\n    // IE11 does not support dataTransfer.items\n    // See https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/items#Browser_compatibility\n    if (dt.items) {\n      const items = fromList(dt.items).filter(item => item.kind === 'file');\n      // According to https://html.spec.whatwg.org/multipage/dnd.html#dndevents,\n      // only 'dragstart' and 'drop' has access to the data (source node)\n      if (type !== 'drop') {\n        return items;\n      }\n      const files = yield Promise.all(items.map(toFilePromises));\n      return noIgnoredFiles(flatten(files));\n    }\n    return noIgnoredFiles(fromList(dt.files).map(file => toFileWithPath(file)));\n  });\n}\nfunction noIgnoredFiles(files) {\n  return files.filter(file => FILES_TO_IGNORE.indexOf(file.name) === -1);\n}\n// IE11 does not support Array.from()\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/from#Browser_compatibility\n// https://developer.mozilla.org/en-US/docs/Web/API/FileList\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItemList\nfunction fromList(items) {\n  if (items === null) {\n    return [];\n  }\n  const files = [];\n  // tslint:disable: prefer-for-of\n  for (let i = 0; i < items.length; i++) {\n    const file = items[i];\n    files.push(file);\n  }\n  return files;\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItem\nfunction toFilePromises(item) {\n  if (typeof item.webkitGetAsEntry !== 'function') {\n    return fromDataTransferItem(item);\n  }\n  const entry = item.webkitGetAsEntry();\n  // Safari supports dropping an image node from a different window and can be retrieved using\n  // the DataTransferItem.getAsFile() API\n  // NOTE: FileSystemEntry.file() throws if trying to get the file\n  if (entry && entry.isDirectory) {\n    return fromDirEntry(entry);\n  }\n  return fromDataTransferItem(item, entry);\n}\nfunction flatten(items) {\n  return items.reduce((acc, files) => [...acc, ...(Array.isArray(files) ? flatten(files) : [files])], []);\n}\nfunction fromDataTransferItem(item, entry) {\n  return __awaiter(this, void 0, void 0, function* () {\n    var _a;\n    // Check if we're in a secure context; due to a bug in Chrome (as far as we know)\n    // the browser crashes when calling this API (yet to be confirmed as a consistent behaviour).\n    //\n    // See:\n    // - https://issues.chromium.org/issues/40186242\n    // - https://github.com/react-dropzone/react-dropzone/issues/1397\n    if (globalThis.isSecureContext && typeof item.getAsFileSystemHandle === 'function') {\n      const h = yield item.getAsFileSystemHandle();\n      if (h === null) {\n        throw new Error(`${item} is not a File`);\n      }\n      // It seems that the handle can be `undefined` (see https://github.com/react-dropzone/file-selector/issues/120),\n      // so we check if it isn't; if it is, the code path continues to the next API (`getAsFile`).\n      if (h !== undefined) {\n        const file = yield h.getFile();\n        file.handle = h;\n        return toFileWithPath(file);\n      }\n    }\n    const file = item.getAsFile();\n    if (!file) {\n      throw new Error(`${item} is not a File`);\n    }\n    const fwp = toFileWithPath(file, (_a = entry === null || entry === void 0 ? void 0 : entry.fullPath) !== null && _a !== void 0 ? _a : undefined);\n    return fwp;\n  });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemEntry\nfunction fromEntry(entry) {\n  return __awaiter(this, void 0, void 0, function* () {\n    return entry.isDirectory ? fromDirEntry(entry) : fromFileEntry(entry);\n  });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry\nfunction fromDirEntry(entry) {\n  const reader = entry.createReader();\n  return new Promise((resolve, reject) => {\n    const entries = [];\n    function readEntries() {\n      // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry/createReader\n      // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryReader/readEntries\n      reader.readEntries(batch => __awaiter(this, void 0, void 0, function* () {\n        if (!batch.length) {\n          // Done reading directory\n          try {\n            const files = yield Promise.all(entries);\n            resolve(files);\n          } catch (err) {\n            reject(err);\n          }\n        } else {\n          const items = Promise.all(batch.map(fromEntry));\n          entries.push(items);\n          // Continue reading\n          readEntries();\n        }\n      }), err => {\n        reject(err);\n      });\n    }\n    readEntries();\n  });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileEntry\nfunction fromFileEntry(entry) {\n  return __awaiter(this, void 0, void 0, function* () {\n    return new Promise((resolve, reject) => {\n      entry.file(file => {\n        const fwp = toFileWithPath(file, entry.fullPath);\n        resolve(fwp);\n      }, err => {\n        reject(err);\n      });\n    });\n  });\n}\n//# sourceMappingURL=file-selector.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}