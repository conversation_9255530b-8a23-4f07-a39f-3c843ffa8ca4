{"version": 3, "file": "FontFlags.js", "sourceRoot": "", "sources": ["../../../src/core/embedders/FontFlags.ts"], "names": [], "mappings": "AAcA,kBAAkB;AAClB,IAAM,aAAa,GAAG,UAAC,OAAwB;IAC7C,IAAI,KAAK,GAAG,CAAC,CAAC;IAEd,IAAM,OAAO,GAAG,UAAC,GAAW,IAAO,KAAK,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAEhE,IAAI,OAAO,CAAC,UAAU;QAAG,OAAO,CAAC,CAAC,CAAC,CAAC;IACpC,IAAI,OAAO,CAAC,KAAK;QAAQ,OAAO,CAAC,CAAC,CAAC,CAAC;IACpC,IAAI,OAAO,CAAC,QAAQ;QAAK,OAAO,CAAC,CAAC,CAAC,CAAC;IACpC,IAAI,OAAO,CAAC,MAAM;QAAO,OAAO,CAAC,CAAC,CAAC,CAAC;IACpC,IAAI,OAAO,CAAC,WAAW;QAAE,OAAO,CAAC,CAAC,CAAC,CAAC;IACpC,IAAI,OAAO,CAAC,MAAM;QAAO,OAAO,CAAC,CAAC,CAAC,CAAC;IACpC,IAAI,OAAO,CAAC,MAAM;QAAO,OAAO,CAAC,EAAE,CAAC,CAAC;IACrC,IAAI,OAAO,CAAC,QAAQ;QAAK,OAAO,CAAC,EAAE,CAAC,CAAC;IACrC,IAAI,OAAO,CAAC,SAAS;QAAI,OAAO,CAAC,EAAE,CAAC,CAAC;IAErC,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAEF,uHAAuH;AACvH,MAAM,CAAC,IAAM,eAAe,GAAG,UAAC,IAAU;IACxC,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;IACjE,IAAM,KAAK,GAAG,aAAa,CAAC;QAC1B,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY;QAClC,KAAK,EAAE,CAAC,IAAI,WAAW,IAAI,WAAW,IAAI,CAAC;QAC3C,QAAQ,EAAE,IAAI;QACd,MAAM,EAAE,WAAW,KAAK,EAAE;QAC1B,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM;KAClC,CAAC,CAAC;IACH,OAAO,KAAK,CAAC;AACf,CAAC,CAAC"}